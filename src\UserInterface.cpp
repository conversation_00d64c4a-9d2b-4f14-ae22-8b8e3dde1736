/*
 * UserInterface.cpp
 * 用户界面类实现
 */

#include "../include/UserInterface.h"
#include <Arduino.h>

// 按键消抖时间（毫秒）- 增加消抖时间以减少误触发
#define DEBOUNCE_TIME 100

// 构造函数
UserInterface::UserInterface() {
  config = InputDeviceConfig();
  initialized = false;
  buttonCount = 0;
  currentMenu = nullptr;
  rootMenu = nullptr;
  currentSelection = 0;
  menuLevel = 0;
  buttonPressCallback = nullptr;
  menuSelectCallback = nullptr;
  menuBackCallback = nullptr;
}

UserInterface::UserInterface(const InputDeviceConfig& cfg) {
  config = cfg;
  initialized = false;
  buttonCount = cfg.buttonCount;
  currentMenu = nullptr;
  rootMenu = nullptr;
  currentSelection = 0;
  menuLevel = 0;
  buttonPressCallback = nullptr;
  menuSelectCallback = nullptr;
  menuBackCallback = nullptr;
  
  // 初始化按键状态
  for (int i = 0; i < 10; i++) {
    buttonStates[i] = ButtonState();
  }
}

// 初始化函数
void UserInterface::init() {
  // 设置按键引脚
  for (int i = 0; i < config.buttonCount; i++) {
    ButtonConfig& btn = config.buttons[i];
    if (btn.pullup) {
      pinMode(btn.pin, INPUT_PULLUP);
    } else {
      pinMode(btn.pin, INPUT);
    }
  }
  
  // 设置编码器引脚
  if (config.type == INPUT_ENCODER) {
    pinMode(config.encoderPinA, INPUT_PULLUP);
    pinMode(config.encoderPinB, INPUT_PULLUP);
    if (config.encoderButtonPin > 0) {
      pinMode(config.encoderButtonPin, INPUT_PULLUP);
    }
  }
  
  // 初始化菜单系统
  initMenuSystem();
  
  initialized = true;
  Serial.println("用户界面初始化完成");
}

// 设置配置
void UserInterface::setConfig(const InputDeviceConfig& cfg) {
  config = cfg;
  buttonCount = cfg.buttonCount;
  if (initialized) {
    init(); // 重新初始化
  }
}

// 获取配置
InputDeviceConfig UserInterface::getConfig() {
  return config;
}

// 处理输入
void UserInterface::processInput() {
  if (!initialized) {
    return;
  }
  
  switch (config.type) {
    case INPUT_BUTTONS:
      processButtons();
      break;
    case INPUT_ENCODER:
      processEncoder();
      break;
    case INPUT_TOUCH:
      processTouch();
      break;
    case INPUT_SERIAL:
      processSerial();
      break;
    default:
      break;
  }
}

// 处理按键输入
void UserInterface::processButtons() {
  for (int i = 0; i < buttonCount; i++) {
    updateButtonState(i);
    
    // 检查按键按下
    if (isButtonPressedInternal(i)) {
      ButtonType buttonType = config.buttons[i].type;
      
      // 调用按键按下回调
      if (buttonPressCallback) {
        buttonPressCallback(buttonType);
      }
      
      // 处理菜单导航
      switch (buttonType) {
        case BUTTON_UP:
          navigateUp();
          break;
        case BUTTON_DOWN:
          navigateDown();
          break;
        case BUTTON_SELECT:
          selectMenu();
          break;
        case BUTTON_BACK:
          backMenu();
          break;
        default:
          break;
      }
    }
  }
}

// 处理编码器输入
void UserInterface::processEncoder() {
  // 这里应该实现编码器输入处理逻辑
  // 由于没有具体的编码器库实现，这里仅提供框架
  
  // 检查编码器旋转
  // int encoderDelta = readEncoder();
  // if (encoderDelta != 0) {
  //   if (encoderDelta > 0) {
  //     navigateUp();
  //   } else {
  //     navigateDown();
  //   }
  // }
  
  // 检查编码器按钮
  // if (isEncoderButtonPressed()) {
  //   selectMenu();
  // }
}

// 处理触摸输入
void UserInterface::processTouch() {
  // 这里应该实现触摸输入处理逻辑
  // 由于没有具体的触摸库实现，这里仅提供框架
}

// 处理串口输入
void UserInterface::processSerial() {
  // 这里应该实现串口输入处理逻辑
  if (Serial.available()) {
    char cmd = Serial.read();
    
    switch (cmd) {
      case 'u': // 上
        navigateUp();
        break;
      case 'd': // 下
        navigateDown();
        break;
      case 's': // 选择
        selectMenu();
        break;
      case 'b': // 返回
        backMenu();
        break;
      default:
        break;
    }
  }
}

// 更新按键状态
void UserInterface::updateButtonState(int index) {
  if (index < 0 || index >= buttonCount) {
    return;
  }
  
  ButtonConfig& btn = config.buttons[index];
  ButtonState& state = buttonStates[index];
  
  // 读取按键状态
  bool currentState = digitalRead(btn.pin);
  if (btn.invert) {
    currentState = !currentState;
  }
  
  // 按键消抖 - 简化逻辑减少内存
  static uint32_t lastDebounceTime = 0;
  if (currentState != state.pressed) {
    if (millis() - lastDebounceTime > DEBOUNCE_TIME) {
      state.pressed = currentState;
      if (state.pressed) {
        state.pressTime = millis();
      } else {
        state.releaseTime = millis();
      }
    }
  } else {
    lastDebounceTime = millis();
  }
}

// 检查按键是否按下 - 优化版本，减少内存使用
bool UserInterface::isButtonPressedInternal(int index) {
  if (index < 0 || index >= buttonCount) {
    return false;
  }
  
  ButtonState& state = buttonStates[index];
  return state.pressed;
}

// 检查按键是否释放 - 优化版本，减少内存使用
bool UserInterface::isButtonReleasedInternal(int index) {
  if (index < 0 || index >= buttonCount) {
    return false;
  }
  
  ButtonState& state = buttonStates[index];
  return state.released;
}

// 检查按键是否长按（内部）
bool UserInterface::isButtonLongPressedInternal(int index) {
  if (index >= buttonCount) {
    return false;
  }
  
  ButtonState& state = buttonStates[index];
  if (state.longPress) {
    // 标记为已处理，避免重复触发
    state.longPress = false;
    return true;
  }
  
  return false;
}

// 检查按键是否双击（内部）
bool UserInterface::isButtonDoubleClickedInternal(int index) {
  if (index >= buttonCount) {
    return false;
  }
  
  ButtonState& state = buttonStates[index];
  if (state.doubleClick) {
    // 标记为已处理，避免重复触发
    state.doubleClick = false;
    return true;
  }
  
  return false;
}

// 初始化菜单系统
void UserInterface::initMenuSystem() {
  // 创建简化菜单结构 - 减少菜单项数量
  rootMenu = new MenuItem[3];  // 从5个减少到3个
  
  // 菜单项：开始测量
  rootMenu[0].text = "测量";
  rootMenu[0].id = 0;
  rootMenu[0].selectable = true;
  
  // 菜单项：设置
  rootMenu[1].text = "设置";
  rootMenu[1].id = 1;
  rootMenu[1].selectable = true;
  
  // 菜单项：关于
  rootMenu[2].text = "关于";
  rootMenu[2].id = 2;
  rootMenu[2].selectable = true;
  
  // 设置当前菜单
  currentMenu = rootMenu;
  currentSelection = 0;
  menuLevel = 0;
}

// 导航菜单
void UserInterface::navigateMenu(int direction) {
  if (!currentMenu) {
    return;
  }
  
  // 找到当前菜单中的可选项目数量
  int selectableCount = 0;
  for (int i = 0; i < 5; i++) {
    if (currentMenu[i].selectable) {
      selectableCount++;
    }
  }
  
  if (selectableCount == 0) {
    return;
  }
  
  // 更新选择
  currentSelection += direction;
  
  // 处理边界情况
  if (currentSelection < 0) {
    currentSelection = selectableCount - 1;
  } else if (currentSelection >= selectableCount) {
    currentSelection = 0;
  }
  
  // 跳过不可选项目
  while (!currentMenu[currentSelection].selectable) {
    currentSelection += direction;
    
    if (currentSelection < 0) {
      currentSelection = selectableCount - 1;
    } else if (currentSelection >= selectableCount) {
      currentSelection = 0;
    }
  }
  
  Serial.print("菜单选择: ");
  Serial.println(currentMenu[currentSelection].text);
}

// 选择菜单项
void UserInterface::selectMenuItem() {
  if (!currentMenu || currentSelection < 0) {
    return;
  }
  
  MenuItem& item = currentMenu[currentSelection];
  
  // 调用菜单选择回调
  if (menuSelectCallback) {
    menuSelectCallback(item.id);
  }
  
  // 执行菜单项回调
  if (item.callback) {
    item.callback();
  }
}

// 返回父菜单
void UserInterface::backToParentMenu() {
  if (menuLevel > 0 && currentMenu && currentMenu->parent) {
    currentMenu = currentMenu->parent;
    menuLevel--;
    currentSelection = 0;
    
    // 调用菜单返回回调
    if (menuBackCallback) {
      menuBackCallback();
    }
  }
}

// 执行菜单回调
void UserInterface::executeMenuCallback() {
  if (!currentMenu || currentSelection < 0) {
    return;
  }
  
  MenuItem& item = currentMenu[currentSelection];
  if (item.callback) {
    item.callback();
  }
}

// 检查按键是否按下
bool UserInterface::isButtonPressed(ButtonType button) {
  for (int i = 0; i < buttonCount; i++) {
    if (config.buttons[i].type == button) {
      return isButtonPressedInternal(i);
    }
  }
  return false;
}

// 检查按键是否释放
bool UserInterface::isButtonReleased(ButtonType button) {
  for (int i = 0; i < buttonCount; i++) {
    if (config.buttons[i].type == button) {
      return isButtonReleasedInternal(i);
    }
  }
  return false;
}

// 检查按键是否长按
bool UserInterface::isButtonLongPressed(ButtonType button) {
  for (int i = 0; i < buttonCount; i++) {
    if (config.buttons[i].type == button) {
      return isButtonLongPressedInternal(i);
    }
  }
  return false;
}

// 检查按键是否双击
bool UserInterface::isButtonDoubleClicked(ButtonType button) {
  for (int i = 0; i < buttonCount; i++) {
    if (config.buttons[i].type == button) {
      return isButtonDoubleClickedInternal(i);
    }
  }
  return false;
}

// 检查上键是否按下
bool UserInterface::isUpButtonPressed() {
  return isButtonPressed(BUTTON_UP);
}

// 检查下键是否按下
bool UserInterface::isDownButtonPressed() {
  return isButtonPressed(BUTTON_DOWN);
}

// 检查左键是否按下
bool UserInterface::isLeftButtonPressed() {
  return isButtonPressed(BUTTON_LEFT);
}

// 检查右键是否按下
bool UserInterface::isRightButtonPressed() {
  return isButtonPressed(BUTTON_RIGHT);
}

// 检查选择键是否按下
bool UserInterface::isSelectButtonPressed() {
  return isButtonPressed(BUTTON_SELECT);
}

// 检查返回键是否按下
bool UserInterface::isBackButtonPressed() {
  return isButtonPressed(BUTTON_BACK);
}

// 检查测量键是否按下
bool UserInterface::isMeasureButtonPressed() {
  return isButtonPressed(BUTTON_MEASURE);
}

// 检查保存键是否按下
bool UserInterface::isSaveButtonPressed() {
  return isButtonPressed(BUTTON_SAVE);
}

// 检查菜单键是否按下
bool UserInterface::isMenuButtonPressed() {
  return isButtonPressed(BUTTON_MENU);
}

// 设置根菜单
void UserInterface::setRootMenu(MenuItem* menu) {
  rootMenu = menu;
  currentMenu = rootMenu;
  currentSelection = 0;
  menuLevel = 0;
}

// 设置当前菜单
void UserInterface::setCurrentMenu(MenuItem* menu) {
  currentMenu = menu;
  currentSelection = 0;
}

// 获取当前菜单
MenuItem* UserInterface::getCurrentMenu() {
  return currentMenu;
}

// 获取菜单选择
int UserInterface::getMenuSelection() {
  return currentSelection;
}

// 设置菜单选择
void UserInterface::setMenuSelection(int selection) {
  currentSelection = selection;
}

// 向上导航
void UserInterface::navigateUp() {
  navigateMenu(-1);
}

// 向下导航
void UserInterface::navigateDown() {
  navigateMenu(1);
}

// 选择菜单
void UserInterface::selectMenu() {
  selectMenuItem();
}

// 返回菜单
void UserInterface::backMenu() {
  backToParentMenu();
}

// 设置按键按下回调
void UserInterface::setButtonPressCallback(void (*callback)(ButtonType button)) {
  buttonPressCallback = callback;
}

// 设置菜单选择回调
void UserInterface::setMenuSelectCallback(void (*callback)(int menuId)) {
  menuSelectCallback = callback;
}

// 设置菜单返回回调
void UserInterface::setMenuBackCallback(void (*callback)()) {
  menuBackCallback = callback;
}

// 检查是否已初始化
bool UserInterface::isInitialized() {
  return initialized;
}

// 检查输入是否可用
bool UserInterface::isInputAvailable() {
  if (!initialized) {
    return false;
  }
  
  // 这里可以添加输入设备可用性检查
  // 例如：检查按键是否连接正常
  
  return true;
}

// 打印按键状态（调试用）
void UserInterface::printButtonStates() {
  for (int i = 0; i < buttonCount; i++) {
    ButtonConfig& btn = config.buttons[i];
    ButtonState& state = buttonStates[i];
    
    Serial.print("按键 ");
    Serial.print(btn.type);
    Serial.print(": ");
    Serial.print(state.pressed ? "按下" : "释放");
    if (state.longPress) Serial.print(" 长按");
    if (state.doubleClick) Serial.print(" 双击");
    Serial.println();
  }
}

// 打印菜单结构（调试用）
void UserInterface::printMenuStructure() {
  Serial.println("菜单结构:");
  if (!currentMenu) {
    Serial.println("无当前菜单");
    return;
  }
  
  for (int i = 0; i < 5; i++) {
    MenuItem& item = currentMenu[i];
    if (strlen(item.text) > 0) {
      Serial.print(i);
      Serial.print(". ");
      Serial.print(item.text);
      if (i == currentSelection) Serial.print(" [选中]");
      if (!item.selectable) Serial.print(" [不可选]");
      Serial.println();
    }
  }
}

// 显示测量结果
void UserInterface::displayMeasurementResult(int agtronValue) {
  Serial.print("测量结果: AGTRON = ");
  Serial.println(agtronValue);
  
  // 这里可以添加通过显示屏显示结果的逻辑
  // 例如：调用display的相关方法来显示结果
}

// 显示状态信息
void UserInterface::displayStatus(const char* status) {
  Serial.print("状态: ");
  Serial.println(status);
  
  // 这里可以添加通过显示屏显示状态信息的逻辑
  // 例如：调用display的相关方法来显示状态
}