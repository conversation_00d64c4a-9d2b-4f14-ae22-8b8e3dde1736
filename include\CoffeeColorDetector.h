/*
 * CoffeeColorDetector.h
 * 咖啡豆色值检测器类定义
 * 
 * 主要功能：
 * 1. 处理颜色数据
 * 2. 分析咖啡豆烘焙程度
 * 3. 计算色值参数
 * 4. 存储和导出结果
 */

#ifndef COFFEE_COLOR_DETECTOR_H
#define COFFEE_COLOR_DETECTOR_H

#include <Arduino.h>

// RGB颜色结构体
struct RGBColor {
  uint16_t red;
  uint16_t green;
  uint16_t blue;
  
  RGBColor() : red(0), green(0), blue(0) {}
  RGBColor(uint16_t r, uint16_t g, uint16_t b) : red(r), green(g), blue(b) {}
};

// Lab颜色空间结构体
struct LabColor {
  float L;  // 亮度
  float a;  // 从绿色到红色
  float b;  // 从蓝色到黄色
  
  LabColor() : L(0), a(0), b(0) {}
  LabColor(float l, float aVal, float bVal) : L(l), a(aVal), b(bVal) {}
};

// HSV颜色空间结构体
struct HSVColor {
  float H;  // 色相
  float S;  // 饱和度
  float V;  // 明度
  
  HSVColor() : H(0), S(0), V(0) {}
  HSVColor(float h, float s, float v) : H(h), S(s), V(v) {}
};

// 烘焙程度枚举
enum RoastLevel {
  ROAST_UNROASTED,     // 未烘焙
  ROAST_LIGHT,         // 浅烘焙
  ROAST_MEDIUM_LIGHT,  // 中浅烘焙
  ROAST_MEDIUM,        // 中烘焙
  ROAST_MEDIUM_DARK,   // 中深烘焙
  ROAST_DARK,          // 深烘焙
  ROAST_VERY_DARK      // 极深烘焙
};

// 颜色分析结果结构体
struct ColorAnalysisResult {
  RGBColor rgb;
  LabColor lab;
  HSVColor hsv;
  RoastLevel roastLevel;
  float roastPercentage;  // 烘焙程度百分比
  int agtronValue;        // AGTRON色值
  const char* roastDescription; // 烘焙程度描述
  uint32_t timestamp;      // 时间戳
  
  ColorAnalysisResult() : roastLevel(ROAST_UNROASTED), roastPercentage(0), agtronValue(0), timestamp(0), roastDescription("") {}
};

// 咖啡豆色值检测器类
class CoffeeColorDetector {
private:
  RGBColor currentRGB;
  LabColor currentLab;
  HSVColor currentHSV;
  ColorAnalysisResult currentResult;
  
  // 颜色空间转换函数
  LabColor rgbToLab(const RGBColor& rgb);
  HSVColor rgbToHsv(const RGBColor& rgb);
  
  // 烘焙程度分析函数
  RoastLevel determineRoastLevel(const LabColor& lab);
  float calculateRoastPercentage(const LabColor& lab);
  String getRoastDescription(RoastLevel level);
  String getRoastDescriptionByAgtron(int agtronValue);
  
  // 数据存储相关
  bool saveToMemory(const ColorAnalysisResult& result);
  bool exportData();
  
public:
  // 构造函数和初始化
  CoffeeColorDetector();
  void init();
  
  // 数据处理函数
  void processColorData(const RGBColor& rgb);
  void analyzeColor();
  void analyzeColor(const RGBColor& rgb);
  void calculateRoastLevel();
  
  // 结果获取函数
  ColorAnalysisResult getAnalysisResult();
  RGBColor getRGB();
  LabColor getLab();
  HSVColor getHSV();
  RoastLevel getRoastLevel();
  float getRoastPercentage();
  int getAgtronValue();
  
  // 数据存储和导出
  bool saveResult();
  bool exportResults();
  
  // 历史记录管理
  int getHistoryCount();
  ColorAnalysisResult getHistoryRecord(int index);
  bool clearHistory();
};

#endif // COFFEE_COLOR_DETECTOR_H