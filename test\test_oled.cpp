/*
 * SimpleOLED测试程序
 * 用于验证SimpleOLED库是否能正确编译和链接
 */

#include "../src/SimpleOLED.h"

// 创建OLED实例 - 使用I2C接口
SimpleOLED oled(128, 64, 0x3C);  // 128x64像素，I2C地址0x3C

void setup() {
  Serial.begin(9600);
  Serial.println("SimpleOLED测试程序启动");
  
  // 初始化OLED显示屏
  oled.begin();
  
  // 清空显示屏
  oled.clear();
  
  // 显示测试文本
  oled.drawText(0, 0, "OLED Test");
  oled.drawText(0, 16, "Compilation OK");
  
  // 更新显示
  oled.display();
  
  Serial.println("OLED初始化完成");
}

void loop() {
  // 简单的闪烁效果
  static bool showText = true;
  static unsigned long lastToggle = 0;
  
  if (millis() - lastToggle > 1000) {
    lastToggle = millis();
    
    if (showText) {
      oled.clear();
      oled.drawText(0, 32, "Blink Test");
      oled.display();
    } else {
      oled.clear();
      oled.display();
    }
    
    showText = !showText;
  }
}