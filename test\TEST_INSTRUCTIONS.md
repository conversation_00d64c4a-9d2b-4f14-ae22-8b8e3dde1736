# 分离I2C测试指南

## 问题原因
Arduino IDE将同一文件夹下的所有`.ino`文件视为同一个项目，导致`setup()`和`loop()`函数冲突。

## 解决方案
将测试程序放在独立的文件夹中，或者使用`.cpp`扩展名。

## 测试步骤

### 方法1：使用独立文件夹（推荐）

1. **创建测试文件夹**
   ```
   在Arduino的sketch文件夹中创建：
   test_separate_i2c/
   └── test_separate_i2c.ino
   ```

2. **复制测试代码**
   - 复制`test/simple_i2c_test.ino`的内容
   - 粘贴到新创建的`test_separate_i2c.ino`中

3. **打开并上传**
   - 用Arduino IDE打开`test_separate_i2c.ino`
   - 编译上传

### 方法2：使用现有测试文件

1. **打开测试文件**
   - 复制`test/simple_i2c_test.ino`的内容
   - 在Arduino IDE中创建新文件
   - 粘贴代码并保存为独立项目

## 预期输出

上传成功后，打开串口监视器（波特率9600），应该看到：

```
=== I2C分离测试 ===
硬件I2C (OLED) 初始化完成
软件I2C (传感器) 初始化完成

--- 设备检测 ---
TCS34725传感器 (D4/D5): ✓ 检测到
OLED显示屏 (A4/A5): ✓ 检测到
🎉 两个设备都正常工作！
```

## 常见问题

### ❌ 设备未检测到

1. **检查接线**
   - TCS34725：D4→SDA, D5→SCL, 5V→VCC, GND→GND
   - OLED：A4→SDA, A5→SCL, 5V→VCC, GND→GND

2. **检查上拉电阻**
   - D4和D5引脚**必须**连接4.7kΩ上拉电阻到5V
   - A4和A5通常不需要额外上拉（Arduino内部已有）

3. **检查电源**
   - 确认两个设备都连接到5V和GND
   - 检查是否有短路或断路

### ❌ 编译错误

1. **缺少库文件**
   - 确保已安装`SoftwareWire`库
   - 通过Arduino库管理器搜索并安装

2. **引脚定义错误**
   - 确认使用的是数字引脚D4/D5
   - 不是模拟引脚A4/A5

## 验证成功后

如果测试通过，说明分离I2C方案工作正常，您可以：

1. 将配置应用到主项目
2. 按照`I2C_PINOUT_GUIDE.md`接线
3. 使用新的引脚配置运行主程序

## 技术支持

如果测试失败，请提供：
1. 串口输出的完整错误信息
2. 您的接线照片或描述
3. 使用的Arduino型号
4. 具体的错误现象