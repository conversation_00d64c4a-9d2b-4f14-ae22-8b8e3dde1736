/*
 * ColorSensor.h
 * 颜色传感器类定义
 * 
 * 主要功能：
 * 1. 初始化颜色传感器
 * 2. 读取RGB颜色值
 * 3. 校准传感器
 * 4. 处理原始数据
 */

#ifndef COLOR_SENSOR_H
#define COLOR_SENSOR_H

#include <Arduino.h>
#include "CoffeeColorDetector.h"
#include "../src/TCS34725.h"

// 传感器类型枚举
enum SensorType {
  SENSOR_TCS3200,   // TCS3200颜色传感器
  SENSOR_TCS34725,  // TCS34725颜色传感器
  SENSOR_APDS9960,  // APDS9960颜色传感器
  SENSOR_CUSTOM     // 自定义传感器
};

// 传感器配置结构体
struct SensorConfig {
  SensorType type;
  uint8_t s0Pin;     // TCS3200 S0引脚
  uint8_t s1Pin;     // TCS3200 S1引脚
  uint8_t s2Pin;     // TCS3200 S2引脚
  uint8_t s3Pin;     // TCS3200 S3引脚
  uint8_t outPin;    // TCS3200 OUT引脚
  uint8_t ledPin;    // LED控制引脚
  bool ledEnabled;   // LED是否启用
  
  // TCS34725配置
  uint8_t sdaPin;    // I2C SDA引脚
  uint8_t sclPin;    // I2C SCL引脚
  uint8_t integrationTime;  // 积分时间
  uint8_t gain;             // 增益
  
  SensorConfig() : 
    type(SENSOR_TCS3200), 
    s0Pin(0), s1Pin(1), s2Pin(2), s3Pin(3), outPin(4), 
    ledPin(5), ledEnabled(true),
    sdaPin(A4), sclPin(A5), integrationTime(0xFF), gain(0x01) {}
};

// 校准数据结构体
struct CalibrationData {
  uint16_t whiteRed;
  uint16_t whiteGreen;
  uint16_t whiteBlue;
  uint16_t blackRed;
  uint16_t blackGreen;
  uint16_t blackBlue;
  bool isCalibrated;
  
  CalibrationData() : 
    whiteRed(255), whiteGreen(255), whiteBlue(255),
    blackRed(0), blackGreen(0), blackBlue(0),
    isCalibrated(false) {}
};

// 颜色传感器类
class ColorSensor {
private:
  SensorConfig config;
  CalibrationData calibration;
  bool initialized;
  TCS34725 tcs34725;  // TCS34725传感器对象
  
  // TCS3200相关函数
  void initTCS3200();
  RGBColor readTCS3200();
  void setTCS3200Frequency(bool freq);
  void setTCS3200Filter(bool red, bool green, bool blue);
  
  // TCS34725相关函数
  void initTCS34725();
  RGBColor readTCS34725();
  void setTCS34725IntegrationTime(uint8_t time);
  void setTCS34725Gain(uint8_t gain);
  
  // 通用函数
  void setupPins();
  void enableLED(bool enable);
  RGBColor applyCalibration(const RGBColor& raw);
  
public:
  // 构造函数
  ColorSensor();
  ColorSensor(const SensorConfig& cfg);
  
  // 初始化和配置
  void init();
  void setConfig(const SensorConfig& cfg);
  SensorConfig getConfig();
  
  // 数据读取
  RGBColor readColor();
  uint16_t readRed();
  uint16_t readGreen();
  uint16_t readBlue();
  uint16_t readClear();
  
  // 校准功能
  void calibrateWhite();
  void calibrateBlack();
  void resetCalibration();
  CalibrationData getCalibrationData();
  void setCalibrationData(const CalibrationData& data);
  
  // EEPROM存储功能
  void saveCalibrationToEEPROM();
  void loadCalibrationFromEEPROM();
  void clearCalibrationFromEEPROM();
  
  // 状态检查
  bool isInitialized();
  bool isAvailable();
  
  // LED控制
  void setLED(bool enable);
  bool isLEDEnabled();
};

#endif // COLOR_SENSOR_H