@echo off
echo ===================================
echo 咖啡豆色值检测仪固件构建脚本
echo ===================================

:: 设置变量
set PROJECT_DIR=%~dp0
set SRC_DIR=%PROJECT_DIR%src
set INCLUDE_DIR=%PROJECT_DIR%include
set LIB_DIR=%PROJECT_DIR%lib
set BUILD_DIR=%PROJECT_DIR%build
set ARDUINO_IDE="C:\Program Files (x86)\Arduino\arduino.exe"

:: 检查Arduino IDE是否安装
if not exist %ARDUINO_IDE% (
    echo 错误: 未找到Arduino IDE
    echo 请从 https://www.arduino.cc/en/software 下载并安装
    pause
    exit /b 1
)

:: 创建构建目录
if not exist %BUILD_DIR% mkdir %BUILD_DIR%

:: 检查项目文件
if not exist %PROJECT_DIR%CoffeeColor.ino (
    echo 错误: 未找到主程序文件 %PROJECT_DIR%CoffeeColor.ino
    pause
    exit /b 1
)

echo 正在编译咖啡豆色值检测仪固件...

:: 使用Arduino IDE编译项目
%ARDUINO_IDE% --verify %PROJECT_DIR%CoffeeColor.ino --board arduino:avr:nano

if %ERRORLEVEL% NEQ 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo 编译成功!

:: 询问是否上传固件
set /p UPLOAD=是否上传固件到Arduino Nano? (y/n): 
if /i "%UPLOAD%"=="y" (
    :: 询问COM端口
    set /p COM_PORT=请输入Arduino Nano连接的COM端口(例如COM3): 
    
    echo 正在上传固件到Arduino Nano...
    %ARDUINO_IDE% --upload %PROJECT_DIR%CoffeeColor.ino --board arduino:avr:nano --port %COM_PORT%
    
    if %ERRORLEVEL% NEQ 0 (
        echo 错误: 上传失败
        pause
        exit /b 1
    )
    
    echo 上传成功!
)

echo ===================================
echo 构建完成
echo ===================================
pause