/*
 * Display.cpp
 * 显示类实现
 */

#include "../include/Display.h"
#include "SimpleOLED.h"
#include <Arduino.h>
#include <Wire.h>

// 显示缓冲区大小 - 减小缓冲区以节省内存
#define BUFFER_SIZE (128 * 32 / 8)  // 减少到原来的一半

// 颜色定义
#define COLOR_BLACK 0
#define COLOR_WHITE 1

// SimpleOLED实例
static SimpleOLED* oled = nullptr;

// 构造函数
Display::Display() {
  config = DisplayConfig();
  initialized = false;
  currentScreen = SCREEN_WELCOME;
  // 移除缓冲区指针 - SimpleOLED管理自己的缓冲区
  // buffer = nullptr;
  // bufferSize = 0;
}

Display::Display(const DisplayConfig& cfg) {
  config = cfg;
  initialized = false;
  currentScreen = SCREEN_WELCOME;
  // 移除缓冲区指针 - SimpleOLED管理自己的缓冲区
  // buffer = nullptr;
  // bufferSize = 0;
}

// 初始化函数
void Display::init() {
  // 移除显示缓冲区分配 - SimpleOLED已经有自己的缓冲区
  // bufferSize = config.width * config.height / 8;
  // buffer = new uint8_t[bufferSize];
  
  // 清空缓冲区
  clearBuffer();
  
  // 根据显示屏类型进行初始化
  switch (config.type) {
    case DISPLAY_OLED_I2C:
      initOLEDI2C();
      break;
    case DISPLAY_OLED_SPI:
      initOLED_SPI();
      break;
    case DISPLAY_LCD_I2C:
      initLCDI2C();
      break;
    case DISPLAY_LCD_SPI:
      initLCD_SPI();
      break;
    case DISPLAY_TFT:
      initTFT();
      break;
    default:
      Serial.println("不支持的显示屏类型");
      return;
  }
  
  initialized = true;
  Serial.println("显示屏初始化完成");
  
  // 显示欢迎界面
  showWelcomeScreen();
}

// 设置配置
void Display::setConfig(const DisplayConfig& cfg) {
  config = cfg;
  if (initialized) {
    // 移除缓冲区释放 - SimpleOLED管理自己的缓冲区
    // if (buffer) {
    //   delete[] buffer;
    // }
    
    // 重新初始化
    init();
  }
}

// 获取配置
DisplayConfig Display::getConfig() {
  return config;
}

// 初始化I2C接口的OLED显示屏
void Display::initOLEDI2C() {
  // 首先扫描I2C设备，检查显示屏是否响应
  Serial.print("扫描I2C地址 0x");
  Serial.println(config.address, HEX);
  
  Wire.beginTransmission(config.address);
  uint8_t error = Wire.endTransmission();
  
  if (error != 0) {
    Serial.print("I2C设备未响应，错误码:");
    Serial.println(error);
    return;
  }
  
  Serial.println("I2C设备响应正常");
  
  // 创建SimpleOLED实例
  oled = new SimpleOLED(config.width, config.height, config.address);
  
  // OLED使用硬件I2C，固定为A4/A5引脚，忽略自定义引脚设置
  // 保持兼容性，但使用硬件I2C引脚
  
  // 初始化显示屏
  oled->begin();
  
  // 设置对比度
  oled->setContrast(255);  // 最大对比度
  
  Serial.println("OLED I2C显示屏初始化完成");
}

// 初始化SPI接口的OLED显示屏
void Display::initOLED_SPI() {
  // 这里应该包含OLED SPI初始化代码
  // 由于没有具体的显示库实现，这里仅提供框架
  
  // 设置SPI引脚
  pinMode(config.csPin, OUTPUT);
  pinMode(config.dcPin, OUTPUT);
  if (config.resetPin > 0) {
    pinMode(config.resetPin, OUTPUT);
  }
  
  // 初始化SPI
  // SPI.begin();
  
  // 初始化显示屏
  // 实际实现中应该发送初始化命令序列
  
  Serial.println("OLED SPI显示屏初始化");
}

// 初始化I2C接口的LCD显示屏
void Display::initLCDI2C() {
  // 这里应该包含LCD I2C初始化代码
  // 由于没有具体的显示库实现，这里仅提供框架
  
  Serial.println("LCD I2C显示屏初始化");
}

// 初始化SPI接口的LCD显示屏
void Display::initLCD_SPI() {
  // 这里应该包含LCD SPI初始化代码
  // 由于没有具体的显示库实现，这里仅提供框架
  
  Serial.println("LCD SPI显示屏初始化");
}

// 初始化TFT显示屏
void Display::initTFT() {
  // 这里应该包含TFT初始化代码
  // 由于没有具体的显示库实现，这里仅提供框架
  
  Serial.println("TFT显示屏初始化");
}

// 更新显示
void Display::updateDisplay() {
  if (!initialized || !oled) {
    return;
  }
  
  // 使用SimpleOLED更新显示
  oled->display();
}

// 清空缓冲区
void Display::clearBuffer() {
  // 移除本地缓冲区操作 - 直接使用SimpleOLED的clear
  if (!oled) {
    return;
  }
  
  oled->clear();
}

// 绘制像素
void Display::drawPixel(int16_t x, int16_t y, uint16_t color) {
  if (!oled || x < 0 || y < 0 || x >= config.width || y >= config.height) {
    return;
  }
  
  oled->drawPixel(x, y, color);
}

// 绘制线条
void Display::drawLine(int16_t x0, int16_t y0, int16_t x1, int16_t y1, uint16_t color) {
  if (!oled) {
    return;
  }
  
  oled->drawLine(x0, y0, x1, y1, color);
}

// 绘制矩形
void Display::drawRect(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color) {
  if (!oled) {
    return;
  }
  
  oled->drawRect(x, y, w, h, color);
}

// 填充矩形
void Display::fillRect(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color) {
  if (!oled) {
    return;
  }
  
  oled->fillRect(x, y, w, h, color);
}

// 绘制圆形
void Display::drawCircle(int16_t x0, int16_t y0, int16_t r, uint16_t color) {
  if (!oled) {
    return;
  }
  
  // SimpleOLED没有内置圆形绘制，使用Bresenham算法实现
  int16_t f = 1 - r;
  int16_t ddF_x = 1;
  int16_t ddF_y = -2 * r;
  int16_t x = 0;
  int16_t y = r;

  drawPixel(x0, y0 + r, color);
  drawPixel(x0, y0 - r, color);
  drawPixel(x0 + r, y0, color);
  drawPixel(x0 - r, y0, color);

  while (x < y) {
    if (f >= 0) {
      y--;
      ddF_y += 2;
      f += ddF_y;
    }
    x++;
    ddF_x += 2;
    f += ddF_x;

    drawPixel(x0 + x, y0 + y, color);
    drawPixel(x0 - x, y0 + y, color);
    drawPixel(x0 + x, y0 - y, color);
    drawPixel(x0 - x, y0 - y, color);
    drawPixel(x0 + y, y0 + x, color);
    drawPixel(x0 - y, y0 + x, color);
    drawPixel(x0 + y, y0 - x, color);
    drawPixel(x0 - y, y0 - x, color);
  }
}

// 填充圆形
void Display::fillCircle(int16_t x0, int16_t y0, int16_t r, uint16_t color) {
  if (!oled) {
    return;
  }
  
  // 使用扫描线算法填充圆形
  for (int16_t y = -r; y <= r; y++) {
    for (int16_t x = -r; x <= r; x++) {
      if (x*x + y*y <= r*r) {
        drawPixel(x0 + x, y0 + y, color);
      }
    }
  }
}

// 绘制文本
void Display::drawText(int16_t x, int16_t y, const char* text, uint16_t color, uint8_t size) {
  if (!oled) {
    return;
  }
  
  oled->drawText(x, y, text, color, size);
  
  // 同时输出到串口用于调试
  Serial.print("显示文本: ");
  Serial.print(text);
  Serial.print(" 位置(");
  Serial.print(x);
  Serial.print(", ");
  Serial.print(y);
  Serial.println(")");
}

// RGB转565格式
uint16_t Display::rgbTo565(uint8_t r, uint8_t g, uint8_t b) {
  return ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);
}

// 获取烘焙程度对应的颜色
uint16_t Display::getRoastLevelColor(RoastLevel level) {
  switch (level) {
    case ROAST_UNROASTED:
      return rgbTo565(139, 90, 43); // 生豆色
    case ROAST_LIGHT:
      return rgbTo565(180, 140, 90); // 浅烘焙色
    case ROAST_MEDIUM_LIGHT:
      return rgbTo565(160, 110, 60); // 中浅烘焙色
    case ROAST_MEDIUM:
      return rgbTo565(140, 80, 40); // 中烘焙色
    case ROAST_MEDIUM_DARK:
      return rgbTo565(100, 50, 20); // 中深烘焙色
    case ROAST_DARK:
      return rgbTo565(60, 30, 10); // 深烘焙色
    default:
      return rgbTo565(0, 0, 0); // 黑色
  }
}

// 绘制欢迎界面
void Display::drawWelcomeScreen() {
  if (!oled) {
    return;
  }
  
  // 清空显示屏
  oled->clear();
  
  // 适配128x32屏幕，重新布局文字位置 - 使用英文
  oled->drawText(10, 2, "Coffee Color", COLOR_WHITE, 1);
  oled->drawText(25, 12, "Detector", COLOR_WHITE, 1);
  oled->drawText(35, 22, "v1.0", COLOR_WHITE, 1);
  
  // 更新显示
  oled->display();
  
  Serial.println("绘制欢迎界面");
}

// 绘制菜单界面
void Display::drawMenuScreen() {
  clearBuffer();
  
  // 绘制标题
  drawText(10, 5, "主菜单", COLOR_WHITE, 1);
  
  // 绘制菜单项
  drawText(10, 20, "1. 开始测量", COLOR_WHITE, 1);
  drawText(10, 30, "2. 历史记录", COLOR_WHITE, 1);
  drawText(10, 40, "3. 设置", COLOR_WHITE, 1);
  drawText(10, 50, "4. 关于", COLOR_WHITE, 1);
  
  updateDisplay();
}

// 绘制测量界面
void Display::drawMeasuringScreen() {
  if (!oled) {
    return;
  }
  
  // 清空显示屏
  oled->clear();
  
  // 适配32像素高度，重新布局 - 使用英文
  oled->drawText(25, 2, "Measuring", COLOR_WHITE, 1);
  oled->drawText(30, 12, "Please Wait", COLOR_WHITE, 1);
  
  // 更新显示
  oled->display();
  
  Serial.println("绘制测量界面");
}

// 绘制结果界面
void Display::drawResultScreen(const ColorAnalysisResult& result) {
  if (!oled) {
    return;
  }
  
  // 清空显示屏
  oled->clear();
  
  // 适配32像素高度，简化显示内容 - 使用英文
  oled->drawText(15, 2, "Result", COLOR_WHITE, 1);
  
  // 只显示AGTRON值和烘焙程度
  char agtronStr[32];
  sprintf(agtronStr, "AG:%.1f", result.agtronValue);
  oled->drawText(10, 12, agtronStr, COLOR_WHITE, 1);
  
  // 绘制烘焙程度（缩短描述）- 使用英文
  char shortDesc[16];
  const char* roastDesc = "Unknown";
  switch (result.roastLevel) {
    case ROAST_UNROASTED: roastDesc = "Raw"; break;
    case ROAST_LIGHT: roastDesc = "Light"; break;
    case ROAST_MEDIUM_LIGHT: roastDesc = "MedLight"; break;
    case ROAST_MEDIUM: roastDesc = "Medium"; break;
    case ROAST_MEDIUM_DARK: roastDesc = "MedDark"; break;
    case ROAST_DARK: roastDesc = "Dark"; break;
  }
  strncpy(shortDesc, roastDesc, 15);
  shortDesc[15] = '\0';
  oled->drawText(5, 22, shortDesc, COLOR_WHITE, 1);
  
  // 更新显示
  oled->display();
  
  Serial.println("绘制结果界面");
}

// 绘制历史记录界面
void Display::drawHistoryScreen() {
  clearBuffer();
  
  // 绘制标题
  drawText(10, 5, "历史记录", COLOR_WHITE, 1);
  
  // 绘制提示
  drawText(10, 30, "暂无记录", COLOR_WHITE, 1);
  
  updateDisplay();
}

// 绘制设置界面
void Display::drawSettingsScreen() {
  clearBuffer();
  
  // 绘制标题
  drawText(10, 5, "设置", COLOR_WHITE, 1);
  
  // 绘制设置项
  drawText(10, 20, "1. 校准传感器", COLOR_WHITE, 1);
  drawText(10, 30, "2. 清除记录", COLOR_WHITE, 1);
  drawText(10, 40, "3. 系统信息", COLOR_WHITE, 1);
  
  updateDisplay();
}

// 绘制关于界面
void Display::drawAboutScreen() {
  clearBuffer();
  
  // 绘制标题
  drawText(10, 5, "关于", COLOR_WHITE, 1);
  
  // 绘制信息
  drawText(10, 20, "咖啡豆色值检测仪", COLOR_WHITE, 1);
  drawText(10, 30, "版本: 1.0", COLOR_WHITE, 1);
  drawText(10, 40, "作者: Coffee Team", COLOR_WHITE, 1);
  
  updateDisplay();
}

// 绘制保存确认界面
void Display::drawSaveConfirmationScreen() {
  clearBuffer();
  
  // 绘制提示
  drawText(20, 25, "保存成功", COLOR_WHITE, 1);
  
  updateDisplay();
}

// 显示欢迎界面
void Display::showWelcomeScreen() {
  currentScreen = SCREEN_WELCOME;
  drawWelcomeScreen();
  display();
}

// 显示界面函数
void Display::showResultScreen(const ColorAnalysisResult& result) {
  currentScreen = SCREEN_RESULT;
  drawResultScreen(result);
}
// 更新结果显示
void Display::updateResult(const ColorAnalysisResult& result) {
  if (currentScreen == SCREEN_RESULT) {
    drawResultScreen(result);
  }
}
// 更新菜单选择
void Display::updateMenu(int selection) {
  if (currentScreen == SCREEN_MENU) {
    drawMenuScreen();
    display();
  }
}

// 更新测量进度
void Display::updateMeasuringProgress(int progress) {
  if (currentScreen == SCREEN_MEASURING) {
    drawMeasuringScreen();
    display();
  }
}

// 更新历史记录
void Display::updateHistory(int index) {
  if (currentScreen == SCREEN_HISTORY) {
    drawHistoryScreen();
    display();
  }
}

// 检查是否已初始化
bool Display::isInitialized() {
  return initialized;
}

// 检查显示屏是否可用
bool Display::isAvailable() {
  if (!initialized) {
    return false;
  }
  
  // 这里可以添加显示屏可用性检查
  // 例如：检查通信是否正常
  
  return true;
}

// 清空显示屏
void Display::clear() {
  if (!oled) {
    return;
  }
  
  oled->clear();
  oled->display();
  
  Serial.println("显示屏已清空");
}

// 显示更新
void Display::display() {
  if (!oled) {
    return;
  }
  
  oled->display();
  Serial.println("显示已更新");
}

// 设置对比度// 显示控制
void Display::setContrast(uint8_t contrast) {
  if (!oled) {
    return;
  }
  
  oled->setContrast(contrast);
  
  Serial.print("设置对比度: ");
  Serial.println(contrast);
}

// 设置亮度// 显示控制
void Display::setBrightness(uint8_t brightness) {
  // OLED显示屏使用对比度控制亮度
  if (!oled) {
    return;
  }
  
  // 将亮度(0-255)转换为对比度(0-255)
  oled->setContrast(brightness);
  
  Serial.print("设置亮度: ");
  Serial.println(brightness);
}

// 设置翻转
void Display::setFlip(bool flip) {
  config.flipScreen = flip;
  if (!oled) {
    return;
  }
  
  // SimpleOLED支持0和180度旋转
  oled->setRotation(flip ? 2 : 0);
  
  Serial.print("设置屏幕翻转: ");
  Serial.println(flip ? "是" : "否");
}

// 设置反转
void Display::setInvert(bool invert) {
  config.invertColors = invert;
  if (!oled) {
    return;
  }
  
  // SimpleOLED通过显示开关模拟反转效果
  if (invert) {
    // 反转显示 - 这里需要特殊处理
    // 由于SimpleOLED没有直接的反转命令，我们通过清除屏幕并重新绘制来实现
    oled->clear();
    oled->display();
  } else {
    // 正常显示
    oled->displayOn(true);
  }
  
  Serial.print("设置颜色反转: ");
  Serial.println(invert ? "是" : "否");
}

// 文本显示
void Display::print(const char* text) {
  if (!oled) {
    return;
  }
  
  // 简单的文本显示，使用默认位置和大小
  oled->drawText(0, 0, text, COLOR_WHITE, 1);
  
  Serial.print("显示文本: ");
  Serial.println(text);
}

// 打印文本并换行
void Display::println(const char* text) {
  // 这里应该实现文本打印并换行的逻辑
  Serial.print("打印文本: ");
  Serial.println(text);
}

// 设置光标位置
void Display::setCursor(int16_t x, int16_t y) {
  // 这里应该实现设置光标位置的逻辑
  Serial.print("设置光标位置: (");
  Serial.print(x);
  Serial.print(", ");
  Serial.print(y);
  Serial.println(")");
}

// 设置文本大小
void Display::setTextSize(uint8_t size) {
  // 这里应该实现设置文本大小的逻辑
  Serial.print("设置文本大小: ");
  Serial.println(size);
}

// 设置文本颜色
void Display::setTextColor(uint16_t color) {
  // 这里应该实现设置文本颜色的逻辑
  Serial.print("设置文本颜色: ");
  Serial.println(color);
}