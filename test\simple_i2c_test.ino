/*
 * 简化I2C分离测试
 * 验证TCS34725(D4/D5)和OLED(A4/A5)能否独立工作
 */

#include <Wire.h>
#include <SoftwareWire.h>

// 软件I2C用于传感器
SoftwareWire sensorWire(4, 5);

// 设备地址
#define SENSOR_ADDR 0x29
#define OLED_ADDR   0x3C

void setup() {
  Serial.begin(9600);
  Serial.println("=== I2C分离测试 ===");
  
  // 初始化硬件I2C (OLED)
  Wire.begin();
  Serial.println("硬件I2C (OLED) 初始化完成");
  
  // 初始化软件I2C (传感器)
  sensorWire.begin();
  Serial.println("软件I2C (传感器) 初始化完成");
  
  // 测试设备连接
  testDevices();
}

void loop() {
  // 每2秒测试一次
  static unsigned long lastTest = 0;
  if (millis() - lastTest > 2000) {
    lastTest = millis();
    testDevices();
  }
}

void testDevices() {
  Serial.println("\n--- 设备检测 ---");
  
  // 测试传感器 (软件I2C)
  Serial.print("TCS34725传感器 (D4/D5): ");
  sensorWire.beginTransmission(SENSOR_ADDR);
  int sensorResult = sensorWire.endTransmission();
  if (sensorResult == 0) {
    Serial.println("✓ 检测到");
  } else {
    Serial.print("✗ 未检测到 (错误:");
    Serial.print(sensorResult);
    Serial.println(")");
  }
  
  // 测试OLED (硬件I2C)
  Serial.print("OLED显示屏 (A4/A5): ");
  Wire.beginTransmission(OLED_ADDR);
  int oledResult = Wire.endTransmission();
  if (oledResult == 0) {
    Serial.println("✓ 检测到");
  } else {
    Serial.print("✗ 未检测到 (错误:");
    Serial.print(oledResult);
    Serial.println(")");
  }
  
  // 状态总结
  if (sensorResult == 0 && oledResult == 0) {
    Serial.println("🎉 两个设备都正常工作！");
  } else if (sensorResult == 0) {
    Serial.println("⚠️ 传感器正常，OLED有问题");
  } else if (oledResult == 0) {
    Serial.println("⚠️ OLED正常，传感器有问题");
  } else {
    Serial.println("❌ 两个设备都有问题");
  }
}