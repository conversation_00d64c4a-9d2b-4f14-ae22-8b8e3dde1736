/*
 * Display.h
 * 显示类定义
 * 
 * 主要功能：
 * 1. 初始化显示屏
 * 2. 显示各种界面
 * 3. 更新显示内容
 * 4. 处理显示效果
 */

#ifndef DISPLAY_H
#define DISPLAY_H

#include <Arduino.h>
#include "CoffeeColorDetector.h"

// 显示屏类型枚举
enum DisplayType {
  DISPLAY_OLED_I2C,    // I2C接口的OLED显示屏
  DISPLAY_OLED_SPI,    // SPI接口的OLED显示屏
  DISPLAY_LCD_I2C,     // I2C接口的LCD显示屏
  DISPLAY_LCD_SPI,     // SPI接口的LCD显示屏
  DISPLAY_TFT,         // TFT显示屏
  DISPLAY_CUSTOM       // 自定义显示屏
};

// 显示屏配置结构体
struct DisplayConfig {
  DisplayType type;
  uint8_t address;     // I2C地址
  uint8_t width;       // 显示宽度
  uint8_t height;      // 显示高度
  uint8_t resetPin;    // 复位引脚
  uint8_t csPin;       // 片选引脚(SPI)
  uint8_t dcPin;       // 数据/命令引脚(SPI)
  uint8_t sdaPin;      // SDA引脚(I2C)
  uint8_t sclPin;      // SCL引脚(I2C)
  bool flipScreen;     // 是否翻转屏幕
  bool invertColors;   // 是否反转颜色
  
  DisplayConfig() : 
    type(DISPLAY_OLED_I2C), 
    address(0x3C), width(128), height(64),
    resetPin(-1), csPin(-1), dcPin(-1),
    sdaPin(-1), sclPin(-1),
    flipScreen(false), invertColors(false) {}
};

// 显示界面类型枚举
enum DisplayScreen {
  SCREEN_WELCOME,      // 欢迎界面
  SCREEN_MENU,         // 菜单界面
  SCREEN_MEASURING,    // 测量界面
  SCREEN_RESULT,       // 结果界面
  SCREEN_HISTORY,      // 历史记录界面
  SCREEN_SETTINGS,     // 设置界面
  SCREEN_ABOUT,        // 关于界面
  SCREEN_SAVE_CONFIRM  // 保存确认界面
};

// 显示类
class Display {
private:
  DisplayConfig config;
  bool initialized;
  DisplayScreen currentScreen;
  
  // 移除本地缓冲区 - SimpleOLED管理自己的缓冲区
  // uint8_t* buffer;
  // uint16_t bufferSize;
  // bool bufferNeedsUpdate;
  
  // 初始化函数
  void initOLEDI2C();
  void initOLED_SPI();
  void initLCDI2C();
  void initLCD_SPI();
  void initTFT();
  
  // 显示更新函数
  void updateDisplay();
  void clearBuffer();
  void drawPixel(int16_t x, int16_t y, uint16_t color);
  void drawLine(int16_t x0, int16_t y0, int16_t x1, int16_t y1, uint16_t color);
  void drawRect(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color);
  void fillRect(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color);
  void drawCircle(int16_t x0, int16_t y0, int16_t r, uint16_t color);
  void fillCircle(int16_t x0, int16_t y0, int16_t r, uint16_t color);
  void drawText(int16_t x, int16_t y, const char* text, uint16_t color, uint8_t size);
  
  // 界面绘制函数
  void drawWelcomeScreen();
  void drawMenuScreen();
  void drawMeasuringScreen();
  void drawResultScreen(const ColorAnalysisResult& result);
  void drawHistoryScreen();
  void drawSettingsScreen();
  void drawAboutScreen();
  void drawSaveConfirmationScreen();
  
  // 颜色转换函数
  uint16_t rgbTo565(uint8_t r, uint8_t g, uint8_t b);
  uint16_t getRoastLevelColor(RoastLevel level);
  
public:
  // 构造函数
  Display();
  Display(const DisplayConfig& cfg);
  
  // 初始化和配置
  void init();
  void setConfig(const DisplayConfig& cfg);
  DisplayConfig getConfig();
  
  // 界面显示函数
  void showWelcomeScreen();
  void showResultScreen(const ColorAnalysisResult& result);
  
  // 内容更新函数
  void updateResult(const ColorAnalysisResult& result);
  void updateMenu(int selection);
  void updateMeasuringProgress(int progress);
  void updateHistory(int index);
  
  // 状态检查
  bool isInitialized();
  bool isAvailable();
  
  // 显示控制
  void clear();
  void display();
  void setContrast(uint8_t contrast);
  void setBrightness(uint8_t brightness);
  void setFlip(bool flip);
  void setInvert(bool invert);
  
  // 文本显示
  void print(const char* text);
  void println(const char* text);
  void setCursor(int16_t x, int16_t y);
  void setTextSize(uint8_t size);
  void setTextColor(uint16_t color);
};

#endif // DISPLAY_H