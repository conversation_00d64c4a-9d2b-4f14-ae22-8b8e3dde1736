/*
 * 咖啡豆色值检测仪主程序
 * 
 * 主要功能：
 * 1. 初始化硬件组件（颜色传感器、显示屏、按键等）
 * 2. 采集咖啡豆颜色数据
 * 3. 处理和分析颜色数据
 * 4. 显示结果
 * 5. 提供用户交互界面
 */

#include <Arduino.h>
#include <EEPROM.h>
#include "include/CoffeeColorDetector.h"
#include "include/ColorSensor.h"
#include "include/Display.h"
#include "include/UserInterface.h"

// 创建对象实例 - 使用指针和动态分配以减少内存使用
static CoffeeColorDetector* detector = nullptr;
static ColorSensor* colorSensor = nullptr;
static Display* display = nullptr;
static UserInterface* ui = nullptr;

// 系统状态 - 简化状态机以减少内存使用
enum SystemState {
  STATE_IDLE,
  STATE_MEASURING,
  STATE_PROCESSING,
  STATE_DISPLAY_RESULT,
  STATE_CALIBRATE_WHITE,
  STATE_CALIBRATE_BLACK
};

static SystemState currentState = STATE_IDLE;

void setup() {
  // 初始化串口通信 - 仅在调试模式下启用
  #if ENABLE_DEBUG
    Serial.begin(9600);
    Serial.println("CoffeeColor");
  #endif
  
  // 初始化EEPROM
  EEPROM.begin();
  
  // 动态创建对象实例 - 减少内存使用
  detector = new CoffeeColorDetector();
  colorSensor = new ColorSensor();
  display = new Display();
  ui = new UserInterface();
  
  if (!detector || !colorSensor || !display || !ui) {
    // 内存分配失败
    #if ENABLE_DEBUG
      Serial.println("内存分配失败!");
    #endif
    while (1) {
      delay(1000);
    }
  }
  
  // 初始化硬件组件
  detector->init();
  
  // 检查颜色传感器状态
  #if ENABLE_DEBUG
    Serial.println("正在初始化颜色传感器...");
  #endif
  colorSensor->init();
  
  if (!colorSensor->isAvailable()) {
    // 显示错误信息
    display->clear();
    display->print("Sensor Error!");
    display->setCursor(0, 16);
    display->print("Check I2C Conn");
    display->display();
    
    while (1) {
      delay(1000); // 停止执行
    }
  }
  
  #if ENABLE_DEBUG
    Serial.println("颜色传感器初始化成功");
  #endif
  
  display->init();
  ui->init();
  
  // 尝试从EEPROM加载校准数据
  colorSensor->loadCalibrationFromEEPROM();
  
  #if ENABLE_DEBUG
    Serial.println("Ready");
  #endif
  
  // 进入空闲状态
  currentState = STATE_IDLE;
}

void loop() {
  // 根据当前状态执行相应操作
  switch (currentState) {
    case STATE_IDLE:
      handleIdleState();
      break;
      
    case STATE_MEASURING:
      handleMeasuringState();
      break;
      
    case STATE_PROCESSING:
      handleProcessingState();
      break;
      
    case STATE_DISPLAY_RESULT:
      handleDisplayResultState();
      break;
      
    case STATE_CALIBRATE_WHITE:
      handleCalibrateWhiteState();
      break;
      
    case STATE_CALIBRATE_BLACK:
      handleCalibrateBlackState();
      break;
  }
  
  // 仅在调试模式下处理串口命令
  #if ENABLE_DEBUG
    processSerialCommands();
  #endif
  
  // 短暂延时，避免过度占用CPU
  delay(50);  // 增加延时以减少CPU使用率
}

// 显示系统状态 - 简化版本
void showSystemStatus() {
  #if ENABLE_DEBUG
    Serial.println("\n=== 系统状态 ===");
    
    // 传感器状态
    Serial.print("颜色传感器: ");
    if (colorSensor->isAvailable()) {
      Serial.println("正常");
    } else {
      Serial.println("故障");
    }
    
    // 校准状态
    CalibrationData calData = colorSensor->getCalibrationData();
    Serial.print("校准状态: ");
    Serial.println(calData.isCalibrated ? "已校准" : "未校准");
    
    // 当前系统状态
    Serial.print("系统状态: ");
    switch (currentState) {
      case STATE_IDLE:
        Serial.println("空闲");
        break;
      case STATE_MEASURING:
        Serial.println("测量中");
        break;
      case STATE_PROCESSING:
        Serial.println("处理中");
        break;
      case STATE_DISPLAY_RESULT:
        Serial.println("显示结果");
        break;
      default:
        Serial.println("未知");
    }
    
    // 检测器状态
    LabColor currentLab = detector->getLab();
    Serial.print("Lab: L=");
    Serial.print(currentLab.L);
    Serial.print(" a=");
    Serial.print(currentLab.a);
    Serial.print(" b=");
    Serial.println(currentLab.b);
    
    Serial.println("================\n");
  #endif
}

// 处理测量状态 - 简化版本
void handleMeasuringState() {
  // 显示测量界面
  display->clear();
  display->print("Measuring...");
  display->display();
  
  // 采集颜色数据
  RGBColor rgb = colorSensor->readColor();
  
  // 处理颜色数据
  detector->processColorData(rgb);
  
  // 转换到处理状态
  currentState = STATE_PROCESSING;
}

// 处理数据处理状态 - 简化版本
void handleProcessingState() {
  // 分析颜色数据
  detector->analyzeColor();
  
  // 计算烘焙程度
  detector->calculateRoastLevel();
  
  // 获取分析结果
  ColorAnalysisResult result = detector->getAnalysisResult();
  
  // 显示测量结果
  ui->displayMeasurementResult(result.agtronValue);
  
  // 转换到显示结果状态
  currentState = STATE_DISPLAY_RESULT;
  display->showResultScreen(result);
}

// 处理显示结果状态 - 简化版本
void handleDisplayResultState() {
  // 等待一段时间后自动返回测量状态
  delay(1500);  // 减少延时
  currentState = STATE_MEASURING;
}

// 处理空闲状态 - 简化版本
void handleIdleState() {
  // 显示欢迎界面
  display->showWelcomeScreen();
  delay(500);  // 减少延时
  
  // 直接进入测量状态
  currentState = STATE_MEASURING;
}

// 处理白色校准状态 - 简化版本
void handleCalibrateWhiteState() {
  display->clear();
  display->print("White Cal...");
  display->display();
  
  // 执行白色校准
  colorSensor->calibrateWhite();
  
  display->clear();
  display->print("White OK");
  display->display();
  
  delay(1000);
  currentState = STATE_IDLE;
}

// 处理黑色校准状态 - 简化版本
void handleCalibrateBlackState() {
  display->clear();
  display->print("Black Cal...");
  display->display();
  
  // 执行黑色校准
  colorSensor->calibrateBlack();
  
  display->clear();
  display->print("Black OK");
  display->display();
  
  delay(1000);
  currentState = STATE_IDLE;
}

// 处理串口命令 - 简化版本，减少内存使用
void processSerialCommands() {
  #if ENABLE_DEBUG
    if (Serial.available()) {
      char command[8];  // 减小命令缓冲区大小
      int index = 0;
      
      // 读取字符直到换行符或缓冲区满
      while (Serial.available() && index < 7) {
        char c = Serial.read();
        if (c == '\n') break;
        if (c >= 32 && c <= 126) {
          command[index++] = c;
        }
      }
      command[index] = '\0';
      
      // 简化命令处理
      if (strcmp(command, "calwhite") == 0) {
        currentState = STATE_CALIBRATE_WHITE;
      } else if (strcmp(command, "calblack") == 0) {
        currentState = STATE_CALIBRATE_BLACK;
      } else if (strcmp(command, "resetcal") == 0) {
        colorSensor->resetCalibration();
      }
    }
  #endif
}