/*
 * 测试分离I2C引脚配置
 * TCS34725传感器使用软件I2C连接D4/D5
 * OLED显示屏使用硬件I2C连接A4/A5
 * 
 * 使用方法：
 * 1. 将此文件放在单独的文件夹中
 * 2. 用Arduino IDE打开这个.ino文件
 * 3. 编译上传
 */

#include <Arduino.h>
#include <SoftwareWire.h>

// 软件I2C库
SoftwareWire sensorWire(4, 5);  // SDA=D4, SCL=D5

// 传感器寄存器定义
#define TCS34725_ADDRESS 0x29
#define TCS34725_ID 0x12
#define TCS34725_COMMAND_BIT 0x80
#define TCS34725_ENABLE 0x00
#define TCS34725_ENABLE_PON 0x01
#define TCS34725_ENABLE_AEN 0x02
#define TCS34725_ATIME 0x01
#define TCS34725_CONTROL 0x0F
#define TCS34725_CDATAL 0x14
#define TCS34725_RDATAL 0x16
#define TCS34725_GDATAL 0x18
#define TCS34725_BDATAL 0x1A

// OLED显示屏定义
#define OLED_ADDRESS 0x3C
#define OLED_WIDTH 128
#define OLED_HEIGHT 32

// 函数声明
bool initTCS34725();
bool initOLED();
void readColorData(uint16_t *r, uint16_t *g, uint16_t *b, uint16_t *c);
void displayOnOLED(uint16_t r, uint16_t g, uint16_t b, uint16_t c);

void setup() {
  Serial.begin(9600);
  Serial.println("=== 分离I2C引脚测试 ===");
  Serial.println("TCS34725传感器: D4/D5 (软件I2C)");
  Serial.println("OLED显示屏: A4/A5 (硬件I2C)");
  
  // 初始化软件I2C用于传感器
  sensorWire.begin();
  
  // 初始化硬件I2C用于OLED
  Wire.begin();
  
  // 测试传感器
  Serial.print("正在初始化TCS34725传感器...");
  if (initTCS34725()) {
    Serial.println("成功!");
  } else {
    Serial.println("失败!");
  }
  
  // 测试OLED
  Serial.print("正在初始化OLED显示屏...");
  if (initOLED()) {
    Serial.println("成功!");
  } else {
    Serial.println("失败!");
  }
  
  Serial.println("测试完成！开始循环读取...");
}

void loop() {
  static unsigned long lastRead = 0;
  if (millis() - lastRead > 1000) {
    lastRead = millis();
    
    uint16_t r, g, b, c;
    readColorData(&r, &g, &b, &c);
    
    // 串口输出
    Serial.print("实时数据 - R:");
    Serial.print(r);
    Serial.print(" G:");
    Serial.print(g);
    Serial.print(" B:");
    Serial.print(b);
    Serial.print(" C:");
    Serial.println(c);
    
    // OLED显示
    displayOnOLED(r, g, b, c);
  }
}

// 初始化TCS34725传感器（软件I2C）
bool initTCS34725() {
  // 检查设备ID
  sensorWire.beginTransmission(TCS34725_ADDRESS);
  sensorWire.write(TCS34725_COMMAND_BIT | 0x12); // ID寄存器
  sensorWire.endTransmission();
  
  sensorWire.requestFrom(TCS34725_ADDRESS, 1);
  if (sensorWire.available()) {
    uint8_t id = sensorWire.read();
    if (id != 0x44 && id != 0x10) { // TCS34721/TCS34725 ID
      return false;
    }
  }
  
  // 启用传感器
  sensorWire.beginTransmission(TCS34725_ADDRESS);
  sensorWire.write(TCS34725_COMMAND_BIT | TCS34725_ENABLE);
  sensorWire.write(TCS34725_ENABLE_PON);
  sensorWire.endTransmission();
  
  delay(3);
  
  sensorWire.beginTransmission(TCS34725_ADDRESS);
  sensorWire.write(TCS34725_COMMAND_BIT | TCS34725_ENABLE);
  sensorWire.write(TCS34725_ENABLE_PON | TCS34725_ENABLE_AEN);
  sensorWire.endTransmission();
  
  // 设置积分时间
  sensorWire.beginTransmission(TCS34725_ADDRESS);
  sensorWire.write(TCS34725_COMMAND_BIT | TCS34725_ATIME);
  sensorWire.write(0xFF); // 2.4ms
  sensorWire.endTransmission();
  
  // 设置增益
  sensorWire.beginTransmission(TCS34725_ADDRESS);
  sensorWire.write(TCS34725_COMMAND_BIT | TCS34725_CONTROL);
  sensorWire.write(0x00); // 1x增益
  sensorWire.endTransmission();
  
  return true;
}

// 初始化OLED显示屏（硬件I2C）
bool initOLED() {
  // 发送初始化命令序列
  Wire.beginTransmission(OLED_ADDRESS);
  Wire.write(0x00); // 命令模式
  Wire.write(0xAE); // 关闭显示
  Wire.write(0xD5); // 设置时钟分频
  Wire.write(0x80);
  Wire.write(0xA8); // 设置多路复用
  Wire.write(0x1F); // 32行
  Wire.write(0xD3); // 设置显示偏移
  Wire.write(0x00);
  Wire.write(0x40); // 设置起始行
  Wire.write(0x8D); // 设置电荷泵
  Wire.write(0x14);
  Wire.write(0x20); // 设置内存模式
  Wire.write(0x00); // 水平寻址
  Wire.write(0xA1); // 设置段重映射
  Wire.write(0xC8); // 设置COM输出扫描方向
  Wire.write(0xDA); // 设置COM引脚
  Wire.write(0x02);
  Wire.write(0x81); // 设置对比度
  Wire.write(0x8F);
  Wire.write(0xD9); // 设置预充电周期
  Wire.write(0xF1);
  Wire.write(0xDB); // 设置VCOMH取消选择电平
  Wire.write(0x40);
  Wire.write(0xA4); // 输出跟随RAM
  Wire.write(0xA6); // 正常显示
  Wire.write(0xAF); // 打开显示
  Wire.endTransmission();
  
  // 清空显示
  for (uint8_t page = 0; page < 4; page++) {
    Wire.beginTransmission(OLED_ADDRESS);
    Wire.write(0x00); // 命令模式
    Wire.write(0xB0 + page); // 设置页地址
    Wire.write(0x00); // 设置低列地址
    Wire.write(0x10); // 设置高列地址
    Wire.endTransmission();
    
    Wire.beginTransmission(OLED_ADDRESS);
    Wire.write(0x40); // 数据模式
    for (uint8_t col = 0; col < 128; col++) {
      Wire.write(0x00); // 清空数据
    }
    Wire.endTransmission();
  }
  
  return true;
}

// 读取颜色数据（软件I2C）
void readColorData(uint16_t *r, uint16_t *g, uint16_t *b, uint16_t *c) {
  // 读取清除数据
  sensorWire.beginTransmission(TCS34725_ADDRESS);
  sensorWire.write(TCS34725_COMMAND_BIT | TCS34725_CDATAL);
  sensorWire.endTransmission();
  
  sensorWire.requestFrom(TCS34725_ADDRESS, 8);
  if (sensorWire.available() >= 8) {
    *c = sensorWire.read() | (sensorWire.read() << 8);
    *r = sensorWire.read() | (sensorWire.read() << 8);
    *g = sensorWire.read() | (sensorWire.read() << 8);
    *b = sensorWire.read() | (sensorWire.read() << 8);
  }
}

// 在OLED上显示数据（硬件I2C）
void displayOnOLED(uint16_t r, uint16_t g, uint16_t b, uint16_t c) {
  // 简单的数据显示 - 只显示第一行
  Wire.beginTransmission(OLED_ADDRESS);
  Wire.write(0x00); // 命令模式
  Wire.write(0xB0); // 页0
  Wire.write(0x00); // 列0
  Wire.write(0x10); // 列高地址
  Wire.endTransmission();
  
  Wire.beginTransmission(OLED_ADDRESS);
  Wire.write(0x40); // 数据模式
  
  // 显示简单的文本模式数据
  char buffer[17];
  sprintf(buffer, "R:%d G:%d", r, g);
  
  // 简化的字符显示（8x8点阵）
  for (int i = 0; i < 16 && buffer[i]; i++) {
    // 这里应该使用字体数据，简化处理
    Wire.write(0xFF); // 显示方块表示字符
  }
  
  // 填充剩余空间
  for (int i = 16; i < 128; i++) {
    Wire.write(0x00);
  }
  Wire.endTransmission();
}