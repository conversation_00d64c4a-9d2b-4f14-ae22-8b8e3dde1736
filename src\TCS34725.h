/*
 * TCS34725.h
 * TCS34725颜色传感器库
 * 
 * 这是一个简化的TCS34725颜色传感器库实现
 */

#ifndef TCS34725_H
#define TCS34725_H

#include <Arduino.h>
#include <Wire.h>
#include <SoftwareWire.h>  // 软件I2C库

// TCS34725寄存器地址
#define TCS34725_ENABLE          0x80
#define TCS34725_ATIME           0x81  // 积分时间
#define TCS34725_WTIME           0x83  // 等待时间
#define TCS34725_AILTL           0x84  // 清晰度中断低阈值低字节
#define TCS34725_AILTH           0x85  // 清晰度中断低阈值高字节
#define TCS34725_AIHTL           0x86  // 清晰度中断高阈值低字节
#define TCS34725_AIHTH           0x87  // 清晰度中断高阈值高字节
#define TCS34725_PERS            0x8C  // 中断持久性过滤器
#define TCS34725_CONFIG          0x8D  // 配置
#define TCS34725_CONTROL         0x8F  // 控制
#define TCS34725_ID              0x92  // 设备ID
#define TCS34725_STATUS          0x93  // 状态
#define TCS34725_CDATAL          0x94  // 清晰度数据低字节
#define TCS34725_CDATAH          0x95  // 清晰度数据高字节
#define TCS34725_RDATAL          0x96  // 红色数据低字节
#define TCS34725_RDATAH          0x97  // 红色数据高字节
#define TCS34725_GDATAL          0x98  // 绿色数据低字节
#define TCS34725_GDATAH          0x99  // 绿色数据高字节
#define TCS34725_BDATAL          0x9A  // 蓝色数据低字节
#define TCS34725_BDATAH          0x9B  // 蓝色数据高字节

// TCS34725启用位
#define TCS34725_ENABLE_AIEN     0x10  // RGBC中断使能
#define TCS34725_ENABLE_WEN      0x08  // 等待使能
#define TCS34725_ENABLE_AEN      0x02  // RGBC使能
#define TCS34725_ENABLE_PON      0x01  // 电源开启

// TCS34725积分时间选项
#define TCS34725_INTEGRATIONTIME_2_4MS  0xFF   // 2.4ms - 1 cycle    - Max Count: 1024
#define TCS34725_INTEGRATIONTIME_24MS   0xF6   // 24ms  - 10 cycles  - Max Count: 10240
#define TCS34725_INTEGRATIONTIME_50MS   0xEB   // 50ms  - 20 cycles  - Max Count: 20480
#define TCS34725_INTEGRATIONTIME_101MS  0xD5   // 101ms - 42 cycles  - Max Count: 43008
#define TCS34725_INTEGRATIONTIME_154MS  0xC0   // 154ms - 64 cycles  - Max Count: 65535
#define TCS34725_INTEGRATIONTIME_700MS  0x00   // 700ms - 256 cycles - Max Count: 65535

// TCS34725增益选项
#define TCS34725_GAIN_1X                  0x00   // No gain
#define TCS34725_GAIN_4X                  0x01   // 4x gain
#define TCS34725_GAIN_16X                 0x10   // 16x gain
#define TCS34725_GAIN_60X                 0x11   // 60x gain

class TCS34725 {
private:
  uint8_t _addr;          // I2C地址
  uint8_t _integrationTime; // 积分时间
  uint8_t _gain;          // 增益
  SoftwareWire *_softWire; // 软件I2C实例
  bool _useSoftWire;      // 是否使用软件I2C
  
  // I2C操作函数
  void write8(uint8_t reg, uint8_t value);
  uint16_t read16(uint8_t reg);
  
public:
  uint8_t read8(uint8_t reg);
  // 构造函数
  TCS34725(uint8_t addr = 0x29, uint8_t integrationTime = TCS34725_INTEGRATIONTIME_50MS, uint8_t gain = TCS34725_GAIN_4X);
  
  // 析构函数
  ~TCS34725();
  
  // 初始化传感器
  bool begin(uint8_t sda = -1, uint8_t scl = -1);
  
  // 设置积分时间
  void setIntegrationTime(uint8_t integrationTime);
  
  // 设置增益
  void setGain(uint8_t gain);
  
  // 获取积分时间
  uint8_t getIntegrationTime();
  
  // 获取增益
  uint8_t getGain();
  
  // 读取原始数据
  void getRawData(uint16_t *r, uint16_t *g, uint16_t *b, uint16_t *c);
  
  // 读取RGB值 (0-255)
  void getRGB(uint8_t *r, uint8_t *g, uint8_t *b);
  
  // 读取色温
  uint16_t calculateColorTemperature(uint16_t r, uint16_t g, uint16_t b);
  
  // 读取亮度
  uint16_t calculateLux(uint16_t r, uint16_t g, uint16_t b);
  
  // 检查中断状态
  bool interruptStatus();
  
  // 清除中断
  void clearInterrupt();
  
  // 设置中断
  void setInterrupt(bool flag);
};

#endif // TCS34725_H