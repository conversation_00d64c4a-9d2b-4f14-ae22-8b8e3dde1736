/*
 * SimpleOLED.cpp
 * 简化的OLED显示屏库实现
 */

#include "SimpleOLED.h"
#include <Wire.h>
#include <SPI.h>
#include <avr/pgmspace.h>

// I2C构造函数 - 完整缓冲区模式，兼容SSD1315
SimpleOLED::SimpleOLED(uint8_t width, uint8_t height, uint8_t address) {
  this->type = OLED_I2C;
  this->width = width;
  this->height = height;
  this->address = address;
  this->sda = 0xFF;  // 初始化为无效值
  this->scl = 0xFF;  // 初始化为无效值
  
  // 禁用页模式，使用完整缓冲区，确保SSD1315正确显示
  this->usePageMode = false;
  this->bufferSize = (width * height) / 8; // 完整缓冲区
  this->buffer = new uint8_t[bufferSize];
  this->currentPage = 0; // 初始化当前页
  
  // 初始化缓冲区
  memset(this->buffer, 0, bufferSize);
}

// SPI构造函数
SimpleOLED::SimpleOLED(uint8_t width, uint8_t height, uint8_t cs, uint8_t dc, uint8_t rst) {
  this->type = OLED_SPI;
  this->width = width;
  this->height = height;
  this->cs = cs;
  this->dc = dc;
  this->rst = rst;
  
  // 计算缓冲区大小 (每个像素1位)
  this->bufferSize = (width * height) / 8;
  this->buffer = new uint8_t[bufferSize];
  
  // 初始化缓冲区
  memset(this->buffer, 0, bufferSize);
}

// 析构函数
SimpleOLED::~SimpleOLED() {
  if (buffer) {
    delete[] buffer;
  }
}

// 重新绘制当前页 - 页模式支持
void SimpleOLED::redrawCurrentPage() {
  // 此方法仅在usePageMode=true时使用
  // 在当前实现中，我们禁用了页模式，所以此函数不会被调用
  // 如果未来需要启用页模式，需要实现完整的重绘逻辑
}

// 绘制数字 - 极简版本
void SimpleOLED::drawDigit(int16_t x, int16_t y, uint8_t digit, uint16_t color) {
  // 3x5像素的数字矩阵
  static const uint8_t digits[10][3] = {
    {0x7, 0x5, 0x7}, // 0: 111, 101, 111
    {0x2, 0x7, 0x2}, // 1: 010, 111, 010
    {0x7, 0x1, 0x7}, // 2: 111, 001, 111
    {0x7, 0x3, 0x7}, // 3: 111, 011, 111
    {0x5, 0x7, 0x1}, // 4: 101, 111, 001
    {0x7, 0x4, 0x7}, // 5: 111, 100, 111
    {0x7, 0x4, 0x7}, // 6: 111, 100, 111 (修正)
    {0x7, 0x2, 0x2}, // 7: 111, 010, 010
    {0x7, 0x7, 0x7}, // 8: 111, 111, 111
    {0x7, 0x5, 0x7}  // 9: 111, 101, 111
  };
  
  if (digit > 9) return;
  
  for (int row = 0; row < 3; row++) {
    uint8_t pattern = digits[digit][row];
    for (int col = 0; col < 3; col++) {
      if (pattern & (1 << (2 - col))) {
        drawPixel(x + col, y + row, color);
      }
    }
  }
}

// 设置I2C引脚
void SimpleOLED::setI2CPins(uint8_t sda, uint8_t scl) {
  // OLED使用硬件I2C，Arduino UNO固定为A4/A5引脚
  // 此方法仅保留用于兼容性，实际使用A4/A5引脚
  this->sda = sda;
  this->scl = scl;
}

// 初始化I2C
void SimpleOLED::initI2C() {
  // 使用硬件I2C，Arduino UNO固定为A4/A5引脚
  Wire.begin();
}

// 初始化SPI
void SimpleOLED::initSPI() {
  // 设置SPI引脚
  pinMode(cs, OUTPUT);
  pinMode(dc, OUTPUT);
  
  if (rst != -1) {
    pinMode(rst, OUTPUT);
    // 硬复位
    digitalWrite(rst, LOW);
    delay(10);
    digitalWrite(rst, HIGH);
    delay(10);
  }
  
  // 初始化SPI
  SPI.begin();
}

// 初始化显示屏
void SimpleOLED::begin() {
  if (type == OLED_I2C) {
    initI2C();
  } else {
    initSPI();
  }
  
  // 发送初始化命令序列 - SSD1315兼容版本
  sendCommand(0xAE); // 关闭显示
  
  // 设置时钟分频比和振荡器频率
  sendCommand(0xD5);
  sendCommand(0x80);
  
  // 设置显示偏移
  sendCommand(0xD3);
  sendCommand(0x00);
  
  // 设置起始行
  sendCommand(0x40);
  
  // 设置多路复用率
  sendCommand(0xA8);
  sendCommand(height - 1);
  
  // 设置COM引脚配置
  sendCommand(0xDA);
  sendCommand(0x12);
  
  // 设置段重映射
  sendCommand(0xA1);
  
  // 设置COM输出方向
  sendCommand(0xC8);
  
  // 设置对比度控制 (SSD1315推荐值)
  sendCommand(0x81);
  sendCommand(0xFF); // 最大对比度
  
  // 设置预充电周期 (SSD1315推荐值)
  sendCommand(0xD9);
  sendCommand(0x22); // 阶段1: 2 DCLK, 阶段2: 2 DCLK
  
  // 设置VCOMH取消选择级别 (SSD1315推荐值)
  sendCommand(0xDB);
  sendCommand(0x20); // ~0.77x VCC
  
  // 设置内存寻址模式
  sendCommand(0x20);
  sendCommand(0x00); // 水平寻址模式
  
  // 设置页地址
  sendCommand(0x22);
  sendCommand(0x00);
  sendCommand((height / 8) - 1);
  
  // 设置列地址
  sendCommand(0x21);
  sendCommand(0x00);
  sendCommand(width - 1);
  
  // 充电泵设置
  sendCommand(0x8D);
  sendCommand(0x14);
  
  // 设置显示模式
  sendCommand(0xA4);
  
  // 设置正常显示
  sendCommand(0xA6);
  
  delay(100); // 等待稳定
  
  // 打开显示
  sendCommand(0xAF);
  
  delay(100); // 等待显示稳定
  
  // 清空显示屏
  clear();
  display();
}

// 发送命令
void SimpleOLED::sendCommand(uint8_t cmd) {
  if (type == OLED_I2C) {
    Wire.beginTransmission(address);
    Wire.write(0x00); // 命令字节
    Wire.write(cmd);
    Wire.endTransmission();
  } else {
    digitalWrite(cs, LOW);
    digitalWrite(dc, LOW); // 命令模式
    SPI.transfer(cmd);
    digitalWrite(cs, HIGH);
  }
}

// 发送数据
void SimpleOLED::sendData(uint8_t data) {
  if (type == OLED_I2C) {
    Wire.beginTransmission(address);
    Wire.write(0x40); // 数据字节
    Wire.write(data);
    Wire.endTransmission();
  } else {
    digitalWrite(cs, LOW);
    digitalWrite(dc, HIGH); // 数据模式
    SPI.transfer(data);
    digitalWrite(cs, HIGH);
  }
}

// 清空显示屏
void SimpleOLED::clear() {
  memset(buffer, 0, bufferSize);
}

// 更新显示 - 页模式版本
void SimpleOLED::display() {
  if (type == OLED_I2C) {
    if (usePageMode) {
      // 页模式：逐页发送数据
      uint8_t pages = height / 8;
      for (uint8_t page = 0; page < pages; page++) {
        currentPage = page;
        
        // 清除当前页缓冲区
        memset(buffer, 0, bufferSize);
        
        // 重新绘制当前页的内容
        redrawCurrentPage();
        
        // 设置列地址
        sendCommand(0x21); // 设置列地址
        sendCommand(0x00); // 起始列地址
        sendCommand(width - 1); // 结束列地址
        
        // 设置页地址
        sendCommand(0x22); // 设置页地址
        sendCommand(page); // 起始页地址
        sendCommand(page); // 结束页地址（只发送当前页）
        
        // 发送数据
        Wire.beginTransmission(address);
        Wire.write(0x40); // 数据模式
        
        // 发送当前页数据
        for (uint16_t i = 0; i < width; i++) {
          Wire.write(buffer[i]);
        }
        Wire.endTransmission();
      }
    } else {
      // 全缓冲模式 - 优化的I2C数据传输
      sendCommand(0x21); // 设置列地址
      sendCommand(0);
      sendCommand(width - 1);
      
      sendCommand(0x22); // 设置页地址
      sendCommand(0);
      sendCommand((height / 8) - 1);
      
      // 发送缓冲区数据 - 优化版本
      uint16_t bytesSent = 0;
      while (bytesSent < bufferSize) {
        Wire.beginTransmission(address);
        Wire.write(0x40); // 数据模式
        
        // 每次发送最多32字节，避免I2C缓冲区溢出
        uint8_t bytesToSend = min((uint16_t)32, bufferSize - bytesSent);
        for (uint16_t i = 0; i < bytesToSend; i++) {
          Wire.write(buffer[bytesSent + i]);
        }
        bytesSent += bytesToSend;
        Wire.endTransmission();
      }
    }
  }
}

// 绘制像素 - 页模式版本
void SimpleOLED::drawPixel(int16_t x, int16_t y, uint16_t color) {
  // 检查边界
  if (x < 0 || x >= width || y < 0 || y >= height) {
    return;
  }
  
  if (usePageMode) {
    // 页模式：只处理当前页(8像素高)
    uint8_t page = y / 8;
    uint8_t bit = y % 8;
    
    if (page == currentPage) {
      if (color) {
        buffer[x] |= (1 << bit);
      } else {
        buffer[x] &= ~(1 << bit);
      }
    }
  } else {
    // 全缓冲模式
    uint16_t byteIndex = x + (y / 8) * width;
    uint8_t bitMask = 1 << (y % 8);
    
    if (color) {
      buffer[byteIndex] |= bitMask;
    } else {
      buffer[byteIndex] &= ~bitMask;
    }
  }
}



// 绘制文本 - 扩展版本，支持数字、字母和基本字符
void SimpleOLED::drawText(int16_t x, int16_t y, const char* text, uint16_t color, uint8_t size) {
  if (size != 1) return; // 只支持大小为1的文本
  
  int16_t cursorX = x;
  int16_t cursorY = y;
  
  while (*text) {
    char c = *text++;
    
    // 处理数字
    if (c >= '0' && c <= '9') {
      drawDigit(cursorX, cursorY, c - '0', color);
      cursorX += 4; // 数字宽度 + 间距
    }
    // 处理大写字母
    else if (c >= 'A' && c <= 'Z') {
      drawLetter(cursorX, cursorY, c, color);
      cursorX += 5; // 字母宽度 + 间距
    }
    // 处理小写字母
    else if (c >= 'a' && c <= 'z') {
      drawLetter(cursorX, cursorY, c - 32, color); // 转换为大写
      cursorX += 5; // 字母宽度 + 间距
    }
    // 处理空格
    else if (c == ' ') {
      cursorX += 3; // 空格宽度
    }
    // 处理小数点
    else if (c == '.') {
      drawPixel(cursorX, cursorY + 4, color);
      cursorX += 2;
    }
    // 处理冒号
    else if (c == ':') {
      drawPixel(cursorX, cursorY + 1, color);
      drawPixel(cursorX, cursorY + 3, color);
      cursorX += 3;
    }
    
    // 如果超出屏幕宽度，换行
    if (cursorX > width - 5) {
      cursorX = x;
      cursorY += 6;
    }
  }
}

// 绘制字母 - 5x7像素字体
void SimpleOLED::drawLetter(int16_t x, int16_t y, char letter, uint16_t color) {
  // 简单的5x7像素字母矩阵 (A-Z)
  static const uint8_t letters[26][5] = {
    {0x7E, 0x11, 0x11, 0x7E, 0x00}, // A
    {0x7F, 0x49, 0x49, 0x36, 0x00}, // B
    {0x3E, 0x41, 0x41, 0x22, 0x00}, // C
    {0x7F, 0x41, 0x41, 0x3E, 0x00}, // D
    {0x7F, 0x49, 0x49, 0x41, 0x00}, // E
    {0x7F, 0x48, 0x48, 0x40, 0x00}, // F
    {0x3E, 0x41, 0x49, 0x2A, 0x00}, // G
    {0x7F, 0x08, 0x08, 0x7F, 0x00}, // H
    {0x00, 0x41, 0x7F, 0x41, 0x00}, // I
    {0x06, 0x01, 0x01, 0x7E, 0x00}, // J
    {0x7F, 0x08, 0x14, 0x63, 0x00}, // K
    {0x7F, 0x01, 0x01, 0x01, 0x00}, // L
    {0x7F, 0x20, 0x10, 0x7F, 0x00}, // M
    {0x7F, 0x20, 0x08, 0x07, 0x00}, // N
    {0x3E, 0x41, 0x41, 0x3E, 0x00}, // O
    {0x7F, 0x48, 0x48, 0x30, 0x00}, // P
    {0x3E, 0x41, 0x21, 0x5E, 0x00}, // Q
    {0x7F, 0x48, 0x4C, 0x33, 0x00}, // R
    {0x32, 0x49, 0x49, 0x26, 0x00}, // S
    {0x40, 0x40, 0x7F, 0x40, 0x40}, // T
    {0x7E, 0x01, 0x01, 0x7E, 0x00}, // U
    {0x60, 0x1E, 0x01, 0x7E, 0x00}, // V
    {0x7F, 0x02, 0x04, 0x02, 0x7F}, // W
    {0x63, 0x14, 0x08, 0x14, 0x63}, // X
    {0x60, 0x10, 0x0F, 0x10, 0x60}, // Y
    {0x43, 0x45, 0x49, 0x51, 0x61}  // Z
  };
  
  // 检查字母范围
  if (letter < 'A' || letter > 'Z') {
    return; // 不支持的字符
  }
  
  int letterIndex = letter - 'A';
  
  // 绘制5x7像素字母
  for (int col = 0; col < 5; col++) {
    uint8_t column = letters[letterIndex][col];
    for (int row = 0; row < 7; row++) {
      if (column & (1 << row)) {
        drawPixel(x + col, y + row, color);
      }
    }
  }
}

// 绘制线条
void SimpleOLED::drawLine(int16_t x0, int16_t y0, int16_t x1, int16_t y1, uint16_t color) {
  // Bresenham算法
  int16_t steep = abs(y1 - y0) > abs(x1 - x0);
  
  if (steep) {
    swap(x0, y0);
    swap(x1, y1);
  }
  
  if (x0 > x1) {
    swap(x0, x1);
    swap(y0, y1);
  }
  
  int16_t dx, dy;
  dx = x1 - x0;
  dy = abs(y1 - y0);
  
  int16_t err = dx / 2;
  int16_t ystep;
  
  if (y0 < y1) {
    ystep = 1;
  } else {
    ystep = -1;
  }
  
  for (; x0 <= x1; x0++) {
    if (steep) {
      drawPixel(y0, x0, color);
    } else {
      drawPixel(x0, y0, color);
    }
    
    err -= dy;
    if (err < 0) {
      y0 += ystep;
      err += dx;
    }
  }
}

// 绘制矩形
void SimpleOLED::drawRect(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color) {
  drawLine(x, y, x + w - 1, y, color);
  drawLine(x, y + h - 1, x + w - 1, y + h - 1, color);
  drawLine(x, y, x, y + h - 1, color);
  drawLine(x + w - 1, y, x + w - 1, y + h - 1, color);
}

// 填充矩形
void SimpleOLED::fillRect(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color) {
  for (int16_t i = x; i < x + w; i++) {
    for (int16_t j = y; j < y + h; j++) {
      drawPixel(i, j, color);
    }
  }
}

// 设置显示方向
void SimpleOLED::setRotation(uint8_t rotation) {
  // 简化实现，只支持0和180度旋转
  if (rotation == 0 || rotation == 2) {
    sendCommand(0xA0 | (rotation & 0x01)); // 段重映射
    sendCommand(0xC0 | ((rotation & 0x02) >> 1)); // COM输出方向
  }
}

// 设置对比度
void SimpleOLED::setContrast(uint8_t contrast) {
  sendCommand(0x81); // 设置对比度控制
  sendCommand(contrast);
}

// 显示开关
void SimpleOLED::displayOn(bool on) {
  if (on) {
    sendCommand(0xAF); // 打开显示
  } else {
    sendCommand(0xAE); // 关闭显示
  }
}