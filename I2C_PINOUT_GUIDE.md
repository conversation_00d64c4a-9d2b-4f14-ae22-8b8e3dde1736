# 分离I2C引脚接线指南

## 概述
本项目使用了两路独立的I2C总线：
- **TCS34725颜色传感器**：使用软件I2C，连接到D4/D5引脚
- **OLED显示屏**：使用硬件I2C，连接到A4/A5引脚

## 接线图

### Arduino UNO连接

```
Arduino UNO  -----  TCS34725颜色传感器
D4 (SDA)      -----  SDA
D5 (SCL)      -----  SCL
5V            -----  VCC
GND           -----  GND

Arduino UNO  -----  OLED显示屏
A4 (SDA)      -----  SDA
A5 (SCL)      -----  SCL
5V            -----  VCC
GND           -----  GND
```

### 详细引脚分配

| 设备 | Arduino引脚 | 功能 | 线缆颜色建议 |
|------|-------------|------|--------------|
| TCS34725 | D4 | SDA (数据线) | 绿色 |
| TCS34725 | D5 | SCL (时钟线) | 黄色 |
| TCS34725 | 5V | VCC (电源) | 红色 |
| TCS34725 | GND | 地线 | 黑色 |
| OLED | A4 | SDA (数据线) | 绿色 |
| OLED | A5 | SCL (时钟线) | 黄色 |
| OLED | 5V | VCC (电源) | 红色 |
| OLED | GND | 地线 | 黑色 |

## 上拉电阻

I2C总线需要上拉电阻：
- **TCS34725 (D4/D5)**：需要4.7kΩ上拉电阻连接到5V
- **OLED (A4/A5)**：Arduino UNO内部已有上拉电阻，通常不需要额外添加

## 测试步骤

1. **硬件连接**
   - 按照上述接线图连接所有设备
   - 确保电源和地线连接正确
   - 检查SDA/SCL线是否正确连接

2. **编译测试程序**
   - 打开`test_separate_i2c.ino`
   - 编译并上传到Arduino

3. **验证功能**
   - 打开串口监视器（波特率9600）
   - 观察传感器数据是否正常读取
   - 检查OLED显示屏是否正常显示

## 常见问题

### Q: 为什么要分离I2C引脚？
A: 分离引脚可以让您更方便地接线和调试，特别是当两个设备需要不同的I2C地址或通信速度时。

### Q: 软件I2C和硬件I2C有什么区别？
A: 
- **硬件I2C**：由Arduino硬件支持，速度快，稳定性好
- **软件I2C**：通过软件模拟，可以在任意数字引脚上实现，但速度稍慢

### Q: 如果传感器读取失败怎么办？
A: 
1. 检查接线是否正确
2. 确认上拉电阻是否连接
3. 检查串口输出是否有错误信息
4. 尝试单独测试每个设备

### Q: 可以换成其他引脚吗？
A: 可以！软件I2C可以使用任意数字引脚，硬件I2C在Arduino UNO上固定为A4/A5。

## 技术细节

### 软件I2C库
本项目使用SoftwareWire库来实现软件I2C，该库提供了与标准Wire库类似的API。

### I2C地址
- TCS34725：0x29
- OLED显示屏：0x3C

### 通信速度
- 硬件I2C：100kHz（标准模式）
- 软件I2C：约50kHz（取决于CPU速度）

## 扩展应用

这种分离I2C的设计可以让您：
1. 连接更多I2C设备
2. 使用不同的I2C速度
3. 更容易调试和故障排除
4. 避免地址冲突问题