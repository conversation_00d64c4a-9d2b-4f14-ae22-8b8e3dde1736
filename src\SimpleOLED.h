/*
 * SimpleOLED.h
 * 简化的OLED显示屏库
 * 
 * 这是一个简化的OLED显示屏库实现，支持I2C和SPI接口
 */

#ifndef SIMPLE_OLED_H
#define SIMPLE_OLED_H

#include <Arduino.h>

// 显示屏类型
#define OLED_I2C 0
#define OLED_SPI 1

// 辅助函数：交换两个值
template<typename T>
inline void swap(T& a, T& b) {
  T temp = a;
  a = b;
  b = temp;
}

class SimpleOLED {
private:
  uint8_t type;       // 显示屏类型 (I2C或SPI)
  uint8_t width;      // 显示宽度
  uint8_t height;     // 显示高度
  uint8_t address;    // I2C地址
  uint8_t cs, dc, rst; // SPI引脚
  uint8_t sda, scl;   // I2C引脚
  uint8_t *buffer;    // 显示缓冲区
  uint16_t bufferSize; // 缓冲区大小
  bool usePageMode;    // 使用页模式减少内存使用
  uint8_t currentPage;  // 当前页（页模式时使用）
  
  // 初始化函数
  void initI2C();
  void initSPI();
  
  // 页模式支持函数
  void redrawCurrentPage();
  
  // 发送命令
  void sendCommand(uint8_t cmd);
  
  // 发送数据
  void sendData(uint8_t data);
  
public:
  // 构造函数 (I2C) - 基本3参数版本
  SimpleOLED(uint8_t width, uint8_t height, uint8_t address);
  
  // 构造函数 (SPI)
  SimpleOLED(uint8_t width, uint8_t height, 
             uint8_t cs, uint8_t dc, uint8_t rst = -1);
  
  // 析构函数
  ~SimpleOLED();
  
  // 设置I2C引脚 (必须在begin()之前调用)
  void setI2CPins(uint8_t sda, uint8_t scl);
  
  // 初始化显示屏
  void begin();
  
  // 清空显示屏
  void clear();
  
  // 更新显示
  void display();
  
  // 绘制像素
  void drawPixel(int16_t x, int16_t y, uint16_t color);
  
  // 绘制文本
  void drawText(int16_t x, int16_t y, const char* text, uint16_t color = 1, uint8_t size = 1);
  void drawDigit(int16_t x, int16_t y, uint8_t digit, uint16_t color);
  void drawLetter(int16_t x, int16_t y, char letter, uint16_t color);
  
  // 绘制线条
  void drawLine(int16_t x0, int16_t y0, int16_t x1, int16_t y1, uint16_t color);
  
  // 绘制矩形
  void drawRect(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color);
  
  // 填充矩形
  void fillRect(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color);
  
  // 设置显示方向
  void setRotation(uint8_t rotation);
  
  // 设置对比度
  void setContrast(uint8_t contrast);
  
  // 显示开关
  void displayOn(bool on);
};

#endif // SIMPLE_OLED_H