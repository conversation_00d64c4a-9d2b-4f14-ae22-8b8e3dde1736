/*
 * config.h
 * 项目配置文件
 * 
 * 定义硬件引脚连接和系统参数
 */

#ifndef CONFIG_H
#define CONFIG_H

// 硬件平台选择
#define PLATFORM_ESP32    1
#define PLATFORM_ARDUINO  2
#define PLATFORM_CUSTOM   3

// 设置当前硬件平台
#define CURRENT_PLATFORM PLATFORM_ARDUINO

// 系统参数
#define SYSTEM_NAME "咖啡豆色值检测仪"
#define SYSTEM_VERSION "1.0.0"
#define SERIAL_BAUDRATE 115200

// 传感器类型选择
#define USE_TCS34725_SENSOR   2

// 设置当前使用的传感器
#define CURRENT_SENSOR USE_TCS34725_SENSOR

// 显示屏类型选择
#define USE_OLED_I2C_DISPLAY  1
#define USE_OLED_SPI_DISPLAY  2
#define USE_LCD_I2C_DISPLAY   3
#define USE_LCD_SPI_DISPLAY   4
#define USE_TFT_DISPLAY       5

// 设置当前使用的显示屏
#define CURRENT_DISPLAY USE_OLED_I2C_DISPLAY

// 输入设备类型选择
#define USE_BUTTON_INPUT       1
#define USE_ENCODER_INPUT      2
#define USE_TOUCH_INPUT        3

// 设置当前使用的输入设备
#define CURRENT_INPUT 0  // 无输入设备

// 数据存储选项
#define USE_EEPROM_STORAGE     1
#define USE_SD_CARD_STORAGE    2

// 设置当前使用的存储方式
#define CURRENT_STORAGE USE_EEPROM_STORAGE

// ESP32引脚定义
#if CURRENT_PLATFORM == PLATFORM_ESP32
  // TCS34725颜色传感器引脚
  #define TCS34725_SDA_PIN  21
  #define TCS34725_SCL_PIN  22
  
  // OLED显示屏引脚 (I2C)
  #define OLED_SDA_PIN      21
  #define OLED_SCL_PIN      22
  #define OLED_RST_PIN      -1  // 不使用复位引脚
  
  // OLED显示屏引脚 (SPI)
  #define OLED_CS_PIN       5
  #define OLED_DC_PIN       4
  #define OLED_RST_PIN      16
  
  // 按键引脚
  #define BUTTON_UP_PIN     18
  #define BUTTON_DOWN_PIN   19
  #define BUTTON_LEFT_PIN   23
  #define BUTTON_RIGHT_PIN  32
  #define BUTTON_SELECT_PIN 33
  #define BUTTON_BACK_PIN   34
  #define BUTTON_MEASURE_PIN 35
  #define BUTTON_SAVE_PIN   36
  
  // 编码器引脚
  #define ENCODER_PIN_A     18
  #define ENCODER_PIN_B     19
  #define ENCODER_BTN_PIN   23
  
  // SD卡引脚
  #define SD_CS_PIN         5
  #define SD_MOSI_PIN       23
  #define SD_MISO_PIN       19
  #define SD_SCK_PIN        18
  
  // LED指示灯引脚
  #define STATUS_LED_PIN    2
  #define ERROR_LED_PIN     4
  
  // 蜂鸣器引脚
  #define BUZZER_PIN        15
#endif

// Arduino引脚定义
#if CURRENT_PLATFORM == PLATFORM_ARDUINO
  // TCS34725颜色传感器引脚 - 使用软件I2C，连接到D4/D5
  #define TCS34725_SDA_PIN  4
  #define TCS34725_SCL_PIN  5
  
  // OLED显示屏引脚 (I2C) - 使用硬件I2C，连接到A4/A5
  #define OLED_SDA_PIN      A4
  #define OLED_SCL_PIN      A5
  #define OLED_RST_PIN      -1  // 不使用复位引脚
  
  // OLED显示屏引脚 (SPI)
  #define OLED_CS_PIN       10
  #define OLED_DC_PIN       9
  #define OLED_RST_PIN      8
  
  // 按键引脚
  #define BUTTON_UP_PIN     2
  #define BUTTON_DOWN_PIN   3
  #define BUTTON_LEFT_PIN   4
  #define BUTTON_RIGHT_PIN  5
  #define BUTTON_SELECT_PIN 6
  #define BUTTON_BACK_PIN   7
  #define BUTTON_MEASURE_PIN 8
  #define BUTTON_SAVE_PIN   9
  
  // 编码器引脚
  #define ENCODER_PIN_A     2
  #define ENCODER_PIN_B     3
  #define ENCODER_BTN_PIN   4
  
  // SD卡引脚
  #define SD_CS_PIN         10
  
  // LED指示灯引脚
  #define STATUS_LED_PIN    13
  #define ERROR_LED_PIN     12
  
  // 蜂鸣器引脚
  #define BUZZER_PIN        11
#endif

// 传感器配置参数
#if CURRENT_SENSOR == USE_TCS34725_SENSOR
  // TCS34725积分时间 (2.4ms, 24ms, 50ms, 101ms, 154ms, 700ms)
  #define TCS34725_INTEGRATION_TIME TCS34725_INTEGRATIONTIME_50MS
  
  // TCS34725增益 (1x, 4x, 16x, 60x)
  #define TCS34725_GAIN TCS34725_GAIN_4X
  
  // TCS34725测量参数
  #define TCS34725_MEASUREMENT_DELAY 50  // 测量延时(ms)
#endif

// 显示屏配置参数
#if CURRENT_DISPLAY == USE_OLED_I2C_DISPLAY
  // OLED I2C地址
  #define OLED_I2C_ADDRESS 0x3C
  
  // OLED分辨率 - 减小尺寸以节省内存
  #define OLED_WIDTH 128
  #define OLED_HEIGHT 32  // 从64减少到32，节省50%缓冲区内存
  
  // OLED显示参数
  #define OLED_FLIP false
  #define OLED_INVERT false
#endif

#if CURRENT_DISPLAY == USE_OLED_SPI_DISPLAY
  // OLED分辨率
  #define OLED_WIDTH 128
  #define OLED_HEIGHT 64
  
  // OLED显示参数
  #define OLED_FLIP false
  #define OLED_INVERT false
#endif

// 输入设备配置参数
#if CURRENT_INPUT == USE_BUTTON_INPUT
  // 按键消抖时间(ms)
  #define BUTTON_DEBOUNCE_TIME 50
  
  // 长按时间阈值(ms)
  #define BUTTON_LONG_PRESS_TIME 1000
  
  // 双击时间阈值(ms)
  #define BUTTON_DOUBLE_CLICK_TIME 300
#endif

#if CURRENT_INPUT == USE_ENCODER_INPUT
  // 编码器参数
  #define ENCODER_STEPS_PER_NOTCH 4  // 每个刻度的步数
#endif

// 存储配置参数
#if CURRENT_STORAGE == USE_EEPROM_STORAGE
  // EEPROM大小 - 减小以节省内存
  #define EEPROM_SIZE 256  // 从512减少到256
  
  // 校准数据存储地址
  #define EEPROM_CALIBRATION_ADDR 0
  
  // 历史记录存储地址
  #define EEPROM_HISTORY_START_ADDR 32  // 从64减少到32
  #define EEPROM_MAX_HISTORY_RECORDS 10  // 从20减少到10
  #define EEPROM_HISTORY_RECORD_SIZE 16  // 从32减少到16，每条记录的大小(字节)
#endif

#if CURRENT_STORAGE == USE_SD_CARD_STORAGE
  // SD卡SPI速度
  #define SD_SPI_SPEED SPI_HALF_SPEED
  
  // 历史记录文件名
  #define SD_HISTORY_FILENAME "coffee_history.csv"
  
  // 配置文件名
  #define SD_CONFIG_FILENAME "coffee_config.ini"
#endif

// 系统功能配置 - 禁用非必要功能以节省内存
// 是否启用调试输出
#define ENABLE_DEBUG false  // 从true改为false

// 是否启用自动保存
#define ENABLE_AUTO_SAVE false  // 从true改为false

// 是否启用蜂鸣器提示
#define ENABLE_BUZZER false  // 从true改为false

// 是否启用LED指示灯
#define ENABLE_STATUS_LED false  // 从true改为false

// 测量次数(取平均值) - 减少次数以节省内存
#define MEASUREMENT_COUNT 2  // 从3减少到2

// 测量间隔(ms)
#define MEASUREMENT_INTERVAL 100

// 自动关机时间(分钟, 0表示禁用)
#define AUTO_SHUTDOWN_TIME 5

// 颜色分析参数
// 烘焙程度阈值
#define ROAST_LIGHT_THRESHOLD 20.0
#define ROAST_MEDIUM_LIGHT_THRESHOLD 35.0
#define ROAST_MEDIUM_THRESHOLD 50.0
#define ROAST_MEDIUM_DARK_THRESHOLD 65.0
#define ROAST_DARK_THRESHOLD 80.0

// 调试宏定义
#if ENABLE_DEBUG
  #define DEBUG_PRINT(x) Serial.print(x)
  #define DEBUG_PRINTLN(x) Serial.println(x)
  #define DEBUG_PRINTF(fmt, ...) Serial.printf(fmt, __VA_ARGS__)
#else
  #define DEBUG_PRINT(x)
  #define DEBUG_PRINTLN(x)
  #define DEBUG_PRINTF(fmt, ...)
#endif

#endif // CONFIG_H