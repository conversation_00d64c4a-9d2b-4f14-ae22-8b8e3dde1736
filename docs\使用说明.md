# 咖啡豆色值检测仪固件使用说明

## 项目概述

本项目是一个基于Arduino Nano的咖啡豆色值检测仪固件，用于测量咖啡豆的颜色值并分析其烘焙程度。该固件支持多种颜色传感器和显示屏，提供直观的用户界面，方便用户进行操作和查看结果。

## 硬件要求

### 主控制器
- Arduino Nano开发板

### 颜色传感器
- TCS34725颜色传感器（RGB颜色传感器，带IR滤波器和白光LED）

### 显示屏（推荐使用）
- 0.96英寸SSD1315 OLED显示屏（I2C接口）
- 其他兼容SSD1306驱动芯片的OLED显示屏（可选）

### 输入设备
- 4个按键（上、下、确定、返回）

### 其他组件
- 面包板和连接线
- 电源（5V）

## 软件要求

- Arduino IDE 1.8.x或更高版本
- Arduino Nano开发板支持包（通常随Arduino IDE一起安装）

## 安装步骤

### 1. 安装Arduino IDE

1. 下载并安装Arduino IDE：https://www.arduino.cc/en/software
2. 安装完成后，打开Arduino IDE

### 2. 安装所需库

1. 打开Arduino IDE，进入"工具" > "管理库"
2. 搜索并安装以下库：
   - Wire（用于I2C通信，通常已预装）
   - Adafruit_GFX（用于图形显示）
   - Adafruit_SSD1306（用于OLED显示屏）
   - Adafruit_TCS34725（用于TCS34725颜色传感器）

### 3. 准备项目文件

1. 将本项目文件复制到Arduino工作目录
2. 确保所有库文件位于正确的位置

## 硬件连接

### Arduino Nano与TCS34725颜色传感器连接

| TCS34725引脚 | Arduino Nano引脚 |
|-------------|-----------------|
| VIN/3V3     | 3.3V            |
| GND         | GND             |
| SCL         | A5              |
| SDA         | A4              |
| INT         | 未连接（可选）  |

### Arduino Nano与SSD1315 OLED显示屏连接（I2C）

| OLED引脚 | Arduino Nano引脚 |
|---------|-----------------|
| VCC     | 3.3V            |
| GND     | GND             |
| SCL     | A5              |
| SDA     | A4              |

### Arduino Nano与按键连接

| 按键功能 | Arduino Nano引脚 |
|---------|-----------------|
| 上       | D2              |
| 下       | D3              |
| 确定     | D4              |
| 返回     | D5              |

## 配置固件

打开`include/config.h`文件，根据您的硬件配置修改以下参数：

```cpp
// 硬件平台选择
// #define CURRENT_PLATFORM PLATFORM_ESP32    // 使用ESP32平台
#define CURRENT_PLATFORM PLATFORM_ARDUINO    // 使用Arduino平台

// 传感器配置
#define CURRENT_SENSOR USE_TCS34725_SENSOR    // 使用TCS34725传感器

// TCS34725传感器配置
#define TCS34725_SDA_PIN D4                  // I2C SDA引脚
#define TCS34725_SCL_PIN D5                  // I2C SCL引脚
#define TCS34725_INTEGRATION_TIME 2          // 积分时间(2.4ms)
#define TCS34725_GAIN TCS34725_GAIN_1X       // 增益(1倍)

// 显示屏配置
#define DISPLAY_TYPE DISPLAY_TYPE_OLED_I2C    // 显示屏类型
#define DISPLAY_WIDTH 128                    // 显示宽度
#define DISPLAY_HEIGHT 64                     // 显示高度
#define DISPLAY_ADDRESS 0x3C                  // I2C地址
#define DISPLAY_SDA A4                       // I2C SDA引脚
#define DISPLAY_SCL A5                       // I2C SCL引脚

// 输入设备配置
#define BUTTON_UP_PIN 2                      // 上键引脚
#define BUTTON_DOWN_PIN 3                    // 下键引脚
#define BUTTON_OK_PIN 4                      // 确定键引脚
#define BUTTON_BACK_PIN 5                    // 返回键引脚
```

## 构建和上传固件

### 使用Arduino IDE

1. 打开Arduino IDE
2. 进入"文件" > "打开"，选择`src/main.cpp`文件
3. 选择正确的开发板："工具" > "开发板" > "Arduino AVR Boards" > "Arduino Nano"
4. 选择正确的处理器："工具" > "处理器" > "ATmega328P (Old Bootloader)" 或 "ATmega328P"
5. 选择正确的端口："工具" > "端口"
6. 点击"上传"按钮编译并上传固件

### 使用命令行（使用提供的构建脚本）

1. 打开命令提示符
2. 进入项目目录
3. 运行构建脚本：
   ```
   build.bat
   ```
4. 按照提示操作，输入Arduino Nano连接的COM端口

## 使用说明

### 基本操作

1. 开机后，系统会显示欢迎界面
2. 使用上下键导航菜单
3. 使用确定键选择菜单项
4. 使用返回键返回上一级菜单

### 主要功能

1. **颜色检测**
   - 将咖啡豆放置在传感器上方
   - 选择"检测颜色"菜单项
   - 系统会自动测量并显示颜色值

2. **烘焙程度分析**
   - 检测颜色后，系统会自动分析烘焙程度
   - 烘焙程度分为：浅烘焙、中浅烘焙、中烘焙、中深烘焙、深烘焙

3. **数据存储**
   - 可以保存检测结果
   - 可以查看历史记录

4. **校准**
   - 可以进行白平衡校准
   - 可以进行黑平衡校准
   - 支持通过串口命令进行校准

   #### 4.1 通过菜单校准
   系统提供了校准功能，用于提高颜色测量的准确性。校准分为白平衡校准和黑平衡校准：
   - 白平衡校准：使用白色参考物进行校准，确保白色测量准确
   - 黑平衡校准：使用黑色参考物进行校准，确保黑色测量准确

   校准步骤：
   1. 进入"设置" > "校准"菜单
   2. 选择"白平衡校准"或"黑平衡校准"
   3. 按照屏幕提示，将相应的参考物放置在传感器上方
   4. 按确定键开始校准
   5. 等待校准完成

   #### 4.2 通过串口命令校准
   除了通过菜单校准外，系统还支持通过串口命令进行校准。连接设备到电脑，使用串口监视器（波特率9600）可以发送以下命令：

   - `calwhite` - 执行白色校准
     - 发送此命令后，系统将进入白色校准状态
     - 将白色参考物放置在传感器上方
     - 系统会自动进行白色校准并保存校准数据到EEPROM
     - 校准完成后，系统会返回空闲状态

   - `calblack` - 执行黑色校准
     - 发送此命令后，系统将进入黑色校准状态
     - 将黑色参考物放置在传感器上方
     - 系统会自动进行黑色校准并保存校准数据到EEPROM
     - 校准完成后，系统会返回空闲状态

   - `resetcal` - 重置校准数据
     - 发送此命令后，系统将清除所有校准数据
     - 包括EEPROM中存储的校准数据
     - 重置后，传感器将恢复到出厂默认状态

   - `help` - 显示帮助信息
     - 显示所有可用的串口命令及其说明

   校准数据会自动保存到EEPROM中，即使断电后也能保留。下次启动系统时，会自动加载上次的校准数据。

5. **烘焙度对照表**
   - 系统根据测量的颜色值分析咖啡豆的烘焙程度
   - 提供AGTRON色值作为参考
   - 烘焙程度分为：极浅烘、浅烘、中浅烘、中烘、中深烘、深烘、极深烘

   #### 5.1 烘焙程度与AGTRON值对照表

   | AGTRON值范围 | 烘焙程度 | 描述 | 特点 |
   |------------|---------|------|------|
   | 80-90 | 极浅烘 | Light Roast | 酸度高，果香明显，口感清淡 |
   | 70-79 | 浅烘 | Cinnamon Roast | 酸度高，香气较淡，豆表干燥 |
   | 60-69 | 中浅烘 | Medium Light Roast | 酸甜平衡，香气开始显现 |
   | 50-59 | 中烘 | Medium Roast | 酸甜平衡，香气丰富，口感圆润 |
   | 40-49 | 中深烘 | Medium Dark Roast | 苦味开始显现，酸度降低，油脂开始出现 |
   | 30-39 | 深烘 | Dark Roast | 苦味明显，酸度低，油脂丰富 |
   | 20-29 | 极深烘 | French Roast | 苦味强烈，几乎无酸度，油脂丰富，可能有烟熏味 |

   #### 5.2 烘焙程度与Lab颜色值关系

   系统使用Lab颜色空间分析咖啡豆颜色，其中：
   - L值：表示亮度，范围0-100，数值越大表示越亮
   - a值：表示从绿色到红色的范围，负值偏绿，正值偏红
   - b值：表示从蓝色到黄色的范围，负值偏蓝，正值偏黄

   随着烘焙程度的加深：
   - L值（亮度）逐渐降低
   - a值（红绿轴）先增大后减小
   - b值（黄蓝轴）逐渐减小（从黄色转向蓝色）

   #### 5.3 烘焙程度建议

   不同烘焙程度适合不同的咖啡豆和冲泡方法：

   - **极浅烘/浅烘**：适合酸度较高的咖啡豆，如耶加雪菲、肯尼亚等，适合手冲、滴滤等冲泡方法，能突出咖啡的果香和酸味。

   - **中浅烘/中烘**：适合大多数咖啡豆，平衡酸度和甜度，适合各种冲泡方法，是最常见的烘焙程度。

   - **中深烘/深烘**：适合酸度较低、醇厚度较高的咖啡豆，如巴西、曼特宁等，适合意式浓缩、法压壶等冲泡方法，能突出咖啡的醇厚感和油脂。

   - **极深烘**：适合制作意式浓缩咖啡的拼配豆，苦味强烈，适合加奶制作拿铁、卡布奇诺等奶咖。

### 菜单结构

```
主菜单
├── 检测颜色
├── 历史记录
│   ├── 查看记录
│   └── 清除记录
├── 设置
│   ├── 校准
│   │   ├── 白平衡校准
│   │   └── 黑平衡校准
│   ├── 显示设置
│   └── 系统设置
└── 关于
```

## 故障排除

### 常见问题

1. **显示屏不显示**
   - 检查连接是否正确
   - 检查I2C地址是否正确（常见地址为0x3C）
   - 检查电源是否正常

2. **颜色传感器不工作**
   - 检查连接是否正确
   - 检查LED是否亮起
   - 尝试重新校准

3. **按键不响应**
   - 检查连接是否正确
   - 检查按键是否损坏
   - 检查引脚配置是否正确

### 调试方法

1. **串口调试**
   - 连接Arduino Nano到电脑
   - 使用串口监视器（波特率9600）查看调试信息

2. **测试程序**
   - 使用`test/test_color_detection.ino`测试各个组件

## 扩展功能

### 添加新的传感器类型

1. 在`include/ColorSensor.h`中添加新的传感器类型枚举
2. 在`src/ColorSensor.cpp`中实现新传感器的初始化和读取方法
3. 在`include/config.h`中添加新传感器的配置选项

### 添加新的显示屏类型

1. 在`include/Display.h`中添加新的显示屏类型枚举
2. 在`src/Display.cpp`中实现新显示屏的初始化和显示方法
3. 在`include/config.h`中添加新显示屏的配置选项

### 添加新的功能

1. 在`include/CoffeeColorDetector.h`中添加新的功能方法
2. 在`src/CoffeeColorDetector.cpp`中实现新功能
3. 在`src/main.cpp`中添加新功能的菜单项和调用

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

