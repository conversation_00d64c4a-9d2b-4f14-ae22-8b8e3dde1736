/*
 * TCS34725.cpp
 * TCS34725颜色传感器库实现
 */

#include "TCS34725.h"

// 构造函数
TCS34725::TCS34725(uint8_t addr, uint8_t integrationTime, uint8_t gain) {
  _addr = addr;
  _integrationTime = integrationTime;
  _gain = gain;
  _softWire = nullptr;
  _useSoftWire = false;
}

// 析构函数
TCS34725::~TCS34725() {
  if (_softWire) {
    delete _softWire;
  }
}

// 初始化传感器
bool TCS34725::begin(uint8_t sda, uint8_t scl) {
  // 如果使用自定义引脚，创建软件I2C实例
  if (sda != (uint8_t)-1 && scl != (uint8_t)-1) {
    _softWire = new SoftwareWire(sda, scl);
    _softWire->begin();
    _useSoftWire = true;
  } else {
    // 使用硬件I2C
    Wire.begin();
    _useSoftWire = false;
  }
  
  // 检查设备ID
  uint8_t id = read8(TCS34725_ID);
  if (id != 0x44 && id != 0x4D && id != 0x10) {
    return false;
  }
  
  // 启用传感器 - 分步骤进行
  write8(TCS34725_ENABLE, TCS34725_ENABLE_PON);
  delay(3); // PON稳定时间
  
  // 检查PON状态
  uint8_t status = read8(TCS34725_ENABLE);
  if (!(status & TCS34725_ENABLE_PON)) {
    return false;
  }
  
  write8(TCS34725_ENABLE, TCS34725_ENABLE_PON | TCS34725_ENABLE_AEN);
  delay(3); // AEN稳定时间
  
  // 设置默认值
  setIntegrationTime(_integrationTime);
  setGain(_gain);
  
  // 验证配置
  uint8_t readTime = read8(TCS34725_ATIME);
  uint8_t readGain = read8(TCS34725_CONTROL);
  
  return true;
}

// 设置积分时间
void TCS34725::setIntegrationTime(uint8_t integrationTime) {
  _integrationTime = integrationTime;
  write8(TCS34725_ATIME, _integrationTime);
}

// 设置增益
void TCS34725::setGain(uint8_t gain) {
  _gain = gain;
  write8(TCS34725_CONTROL, _gain);
}

// 获取积分时间
uint8_t TCS34725::getIntegrationTime() {
  return _integrationTime;
}

// 获取增益
uint8_t TCS34725::getGain() {
  return _gain;
}

// 读取原始数据
void TCS34725::getRawData(uint16_t *r, uint16_t *g, uint16_t *b, uint16_t *c) {
  // 等待数据就绪
  while (!(read8(TCS34725_STATUS) & 0x01)) {
    delay(1);
  }
  
  // 读取数据
  *c = read16(TCS34725_CDATAL);
  *r = read16(TCS34725_RDATAL);
  *g = read16(TCS34725_GDATAL);
  *b = read16(TCS34725_BDATAL);
  
  // 根据积分时间设置延迟
  switch (_integrationTime) {
    case TCS34725_INTEGRATIONTIME_2_4MS:
      delay(3);
      break;
    case TCS34725_INTEGRATIONTIME_24MS:
      delay(24);
      break;
    case TCS34725_INTEGRATIONTIME_50MS:
      delay(50);
      break;
    case TCS34725_INTEGRATIONTIME_101MS:
      delay(101);
      break;
    case TCS34725_INTEGRATIONTIME_154MS:
      delay(154);
      break;
    case TCS34725_INTEGRATIONTIME_700MS:
      delay(700);
      break;
  }
}

// 读取RGB值 (0-255)
void TCS34725::getRGB(uint8_t *r, uint8_t *g, uint8_t *b) {
  uint16_t red, green, blue, clear;
  getRawData(&red, &green, &blue, &clear);
  
  // 检查传感器是否就绪
  if (!(read8(TCS34725_ENABLE) & TCS34725_ENABLE_AEN)) {
    *r = *g = *b = 128;
    return;
  }
  
  // 验证原始数据范围
  if (red == 0 && green == 0 && blue == 0 && clear == 0) {
    *r = *g = *b = 128;
    return;
  }
  
  // 避免除以零
  if (clear == 0) {
    *r = *g = *b = 0;
    return;
  }
  
  // 如果clear值太小，返回默认值
  if (clear < 100) {
    *r = *g = *b = 128;
    return;
  }
  
  // 转换为0-255范围
  *r = (uint8_t)((float)red / clear * 255.0);
  *g = (uint8_t)((float)green / clear * 255.0);
  *b = (uint8_t)((float)blue / clear * 255.0);
  
  // 确保值在有效范围内
  *r = min(*r, (uint8_t)255);
  *g = min(*g, (uint8_t)255);
  *b = min(*b, (uint8_t)255);
}

// 读取色温
uint16_t TCS34725::calculateColorTemperature(uint16_t r, uint16_t g, uint16_t b) {
  float X, Y, Z;
  float xc, yc;
  float n;
  float cct;
  
  // 三刺激值 (使用CIE 1931 2°标准观察者)
  X = (-0.14282F * r) + (1.54924F * g) + (-0.95641F * b);
  Y = (-0.32466F * r) + (1.57837F * g) + (-0.73191F * b);
  Z = (-0.68202F * r) + (0.77073F * g) + (0.56332F * b);
  
  // 色度坐标
  xc = X / (X + Y + Z);
  yc = Y / (X + Y + Z);
  
  // McCamy公式计算色温
  n = (xc - 0.3320F) / (0.1858F - yc);
  cct = (449.0F * pow(n, 3)) + (3525.0F * pow(n, 2)) + (6823.3F * n) + 5520.33F;
  
  return (uint16_t)cct;
}

// 读取亮度
uint16_t TCS34725::calculateLux(uint16_t r, uint16_t g, uint16_t b) {
  float illuminance;
  
  // 使用绿色通道计算亮度
  illuminance = (-0.32466F * r) + (1.57837F * g) + (-0.73191F * b);
  
  return (uint16_t)illuminance;
}

// 检查中断状态
bool TCS34725::interruptStatus() {
  return (read8(TCS34725_STATUS) & TCS34725_ENABLE_AIEN);
}

// 清除中断
void TCS34725::clearInterrupt() {
  if (_useSoftWire) {
    _softWire->beginTransmission(_addr);
    _softWire->write(0x66);
    _softWire->write(0x00);
    _softWire->endTransmission();
  } else {
    Wire.beginTransmission(_addr);
    Wire.write(0x66);
    Wire.write(0x00);
    Wire.endTransmission();
  }
}

// 设置中断
void TCS34725::setInterrupt(bool flag) {
  uint8_t enable = read8(TCS34725_ENABLE);
  if (flag) {
    enable |= TCS34725_ENABLE_AIEN;
  } else {
    enable &= ~TCS34725_ENABLE_AIEN;
  }
  write8(TCS34725_ENABLE, enable);
}

// I2C操作函数
void TCS34725::write8(uint8_t reg, uint8_t value) {
  if (_useSoftWire) {
    _softWire->beginTransmission(_addr);
    _softWire->write(reg);
    _softWire->write(value);
    _softWire->endTransmission();
  } else {
    Wire.beginTransmission(_addr);
    Wire.write(reg);
    Wire.write(value);
    Wire.endTransmission();
  }
}

uint8_t TCS34725::read8(uint8_t reg) {
  if (_useSoftWire) {
    _softWire->beginTransmission(_addr);
    _softWire->write(reg);
    _softWire->endTransmission();
    
    _softWire->requestFrom(_addr, (uint8_t)1);
    return _softWire->read();
  } else {
    Wire.beginTransmission(_addr);
    Wire.write(reg);
    Wire.endTransmission();
    
    Wire.requestFrom(_addr, (uint8_t)1);
    return Wire.read();
  }
}

uint16_t TCS34725::read16(uint8_t reg) {
  uint16_t value;
  
  if (_useSoftWire) {
    _softWire->beginTransmission(_addr);
    _softWire->write(reg);
    _softWire->endTransmission();
    
    _softWire->requestFrom(_addr, (uint8_t)2);
    value = _softWire->read();
    value <<= 8;
    value |= _softWire->read();
  } else {
    Wire.beginTransmission(_addr);
    Wire.write(reg);
    Wire.endTransmission();
    
    Wire.requestFrom(_addr, (uint8_t)2);
    value = Wire.read();
    value <<= 8;
    value |= Wire.read();
  }
  
  return value;
}