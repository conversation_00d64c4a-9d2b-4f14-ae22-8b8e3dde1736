/*
 * test_color_detection.ino
 * 咖啡豆色值检测仪测试程序
 * 
 * 这个程序用于测试咖啡豆色值检测仪的各项功能
 */

#include <Arduino.h>
#include "config.h"
#include "CoffeeColorDetector.h"
#include "ColorSensor.h"
#include "Display.h"
#include "UserInterface.h"

// 创建对象实例
CoffeeColorDetector detector;
ColorSensor sensor(COLOR_SENSOR_TYPE, S0_PIN, S1_PIN, S2_PIN, S3_PIN, OUT_PIN, LED_PIN);
Display display(DISPLAY_TYPE, DISPLAY_WIDTH, DISPLAY_HEIGHT, DISPLAY_CS, DISPLAY_DC, DISPLAY_RST);
UserInterface ui(BUTTON_UP_PIN, BUTTON_DOWN_PIN, BUTTON_OK_PIN, BUTTON_BACK_PIN);

// 测试状态
enum TestState {
  TEST_IDLE,
  TEST_SENSOR,
  TEST_DISPLAY,
  TEST_UI,
  TEST_DETECTOR,
  TEST_COMPLETE
};

TestState currentState = TEST_IDLE;
unsigned long lastTestTime = 0;
const unsigned long testInterval = 2000; // 测试间隔2秒

void setup() {
  // 初始化串口
  Serial.begin(115200);
  Serial.println("咖啡豆色值检测仪测试程序启动");
  
  // 初始化传感器
  if (!sensor.begin()) {
    Serial.println("颜色传感器初始化失败");
    while (1);
  }
  Serial.println("颜色传感器初始化成功");
  
  // 初始化显示屏
  if (!display.begin()) {
    Serial.println("显示屏初始化失败");
    while (1);
  }
  Serial.println("显示屏初始化成功");
  
  // 初始化用户界面
  if (!ui.begin()) {
    Serial.println("用户界面初始化失败");
    while (1);
  }
  Serial.println("用户界面初始化成功");
  
  // 初始化检测器
  if (!detector.begin(&sensor, &display, &ui)) {
    Serial.println("检测器初始化失败");
    while (1);
  }
  Serial.println("检测器初始化成功");
  
  // 显示欢迎信息
  display.clear();
  display.drawText(0, 0, "Coffee Color");
  display.drawText(0, 16, "Detector Test");
  display.display();
  
  Serial.println("系统初始化完成，进入测试模式");
  Serial.println("按OK键开始测试");
}

void loop() {
  // 处理用户输入
  ButtonState buttonState = ui.getButtonState();
  
  // 检查按钮状态
  if (buttonState.okPressed) {
    if (currentState == TEST_IDLE) {
      currentState = TEST_SENSOR;
      Serial.println("开始测试颜色传感器");
      display.clear();
      display.drawText(0, 0, "Testing Sensor");
      display.display();
    }
  }
  
  // 根据当前状态执行测试
  switch (currentState) {
    case TEST_SENSOR:
      testColorSensor();
      break;
      
    case TEST_DISPLAY:
      testDisplay();
      break;
      
    case TEST_UI:
      testUserInterface();
      break;
      
    case TEST_DETECTOR:
      testDetector();
      break;
      
    case TEST_COMPLETE:
      display.clear();
      display.drawText(0, 0, "All Tests");
      display.drawText(0, 16, "Complete!");
      display.display();
      Serial.println("所有测试完成");
      currentState = TEST_IDLE;
      break;
      
    default:
      // 空闲状态，等待用户输入
      break;
  }
  
  // 延迟
  delay(100);
}

// 测试颜色传感器
void testColorSensor() {
  unsigned long currentTime = millis();
  
  // 每隔一段时间进行一次测试
  if (currentTime - lastTestTime >= testInterval) {
    lastTestTime = currentTime;
    
    // 读取颜色值
    uint16_t r, g, b;
    sensor.readRGB(&r, &g, &b);
    
    // 打印结果
    Serial.print("RGB值: R=");
    Serial.print(r);
    Serial.print(", G=");
    Serial.print(g);
    Serial.print(", B=");
    Serial.println(b);
    
    // 显示结果
    display.clear();
    display.drawText(0, 0, "Sensor Test");
    display.drawText(0, 16, "R:");
    display.drawText(20, 16, String(r).c_str());
    display.drawText(0, 32, "G:");
    display.drawText(20, 32, String(g).c_str());
    display.drawText(0, 48, "B:");
    display.drawText(20, 48, String(b).c_str());
    display.display();
    
    // 检查是否继续测试
    ButtonState buttonState = ui.getButtonState();
    if (buttonState.okPressed) {
      currentState = TEST_DISPLAY;
      Serial.println("开始测试显示屏");
      display.clear();
      display.drawText(0, 0, "Testing Display");
      display.display();
    }
  }
}

// 测试显示屏
void testDisplay() {
  unsigned long currentTime = millis();
  
  // 每隔一段时间进行一次测试
  if (currentTime - lastTestTime >= testInterval) {
    lastTestTime = currentTime;
    
    static int testStep = 0;
    
    display.clear();
    
    switch (testStep) {
      case 0:
        // 测试文本显示
        display.drawText(0, 0, "Display Test");
        display.drawText(0, 16, "Text Display");
        break;
        
      case 1:
        // 测试线条
        display.drawLine(0, 0, 127, 63, 1);
        display.drawLine(127, 0, 0, 63, 1);
        display.drawText(40, 24, "Lines");
        break;
        
      case 2:
        // 测试矩形
        display.drawRect(10, 10, 50, 30, 1);
        display.drawRect(70, 20, 40, 40, 1);
        display.drawText(40, 24, "Rects");
        break;
        
      case 3:
        // 测试填充矩形
        display.fillRect(10, 10, 50, 30, 1);
        display.fillRect(70, 20, 40, 40, 1);
        display.drawText(40, 24, "Filled");
        break;
    }
    
    display.display();
    
    testStep = (testStep + 1) % 4;
    
    // 检查是否继续测试
    ButtonState buttonState = ui.getButtonState();
    if (buttonState.okPressed) {
      currentState = TEST_UI;
      Serial.println("开始测试用户界面");
      display.clear();
      display.drawText(0, 0, "Testing UI");
      display.display();
    }
  }
}

// 测试用户界面
void testUserInterface() {
  unsigned long currentTime = millis();
  
  // 每隔一段时间进行一次测试
  if (currentTime - lastTestTime >= testInterval) {
    lastTestTime = currentTime;
    
    // 获取按钮状态
    ButtonState buttonState = ui.getButtonState();
    
    display.clear();
    display.drawText(0, 0, "UI Test");
    
    // 显示按钮状态
    display.drawText(0, 16, "Up:");
    display.drawText(30, 16, buttonState.upPressed ? "Pressed" : "Released");
    
    display.drawText(0, 32, "Down:");
    display.drawText(30, 32, buttonState.downPressed ? "Pressed" : "Released");
    
    display.drawText(0, 48, "OK:");
    display.drawText(30, 48, buttonState.okPressed ? "Pressed" : "Released");
    
    display.display();
    
    // 检查是否继续测试
    if (buttonState.okPressed) {
      currentState = TEST_DETECTOR;
      Serial.println("开始测试检测器");
      display.clear();
      display.drawText(0, 0, "Testing Detector");
      display.display();
    }
  }
}

// 测试检测器
void testDetector() {
  unsigned long currentTime = millis();
  
  // 每隔一段时间进行一次测试
  if (currentTime - lastTestTime >= testInterval) {
    lastTestTime = currentTime;
    
    // 执行检测
    ColorAnalysisResult result = detector.detectColor();
    
    // 打印结果
    Serial.print("检测结果: ");
    Serial.print("R=");
    Serial.print(result.rgb.r);
    Serial.print(", G=");
    Serial.print(result.rgb.g);
    Serial.print(", B=");
    Serial.print(result.rgb.b);
    Serial.print(", L=");
    Serial.print(result.lab.l);
    Serial.print(", a=");
    Serial.print(result.lab.a);
    Serial.print(", b=");
    Serial.print(result.lab.b);
    Serial.print(", 烘焙程度=");
    Serial.println(result.roastLevel);
    
    // 显示结果
    display.clear();
    display.drawText(0, 0, "Detector Test");
    
    // 显示RGB值
    display.drawText(0, 16, "RGB:");
    char rgbStr[20];
    sprintf(rgbStr, "%d,%d,%d", result.rgb.r, result.rgb.g, result.rgb.b);
    display.drawText(30, 16, rgbStr);
    
    // 显示烘焙程度
    display.drawText(0, 32, "Roast:");
    const char* roastStr;
    switch (result.roastLevel) {
      case ROAST_LIGHT: roastStr = "Light"; break;
      case ROAST_MEDIUM_LIGHT: roastStr = "Med-Light"; break;
      case ROAST_MEDIUM: roastStr = "Medium"; break;
      case ROAST_MEDIUM_DARK: roastStr = "Med-Dark"; break;
      case ROAST_DARK: roastStr = "Dark"; break;
      default: roastStr = "Unknown"; break;
    }
    display.drawText(30, 32, roastStr);
    
    display.display();
    
    // 检查是否继续测试
    ButtonState buttonState = ui.getButtonState();
    if (buttonState.okPressed) {
      currentState = TEST_COMPLETE;
    }
  }
}