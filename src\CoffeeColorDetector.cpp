/*
 * CoffeeColorDetector.cpp
 * 咖啡豆色值检测器类实现
 */

#include "../include/CoffeeColorDetector.h"
#include <EEPROM.h>

// EEPROM地址定义
#define EEPROM_ADDR_CALIBRATION 0
#define EEPROM_ADDR_HISTORY_START 64
#define MAX_HISTORY_RECORDS 50

// 烘焙程度阈值定义
#define ROAST_LIGHT_THRESHOLD 20.0
#define ROAST_MEDIUM_LIGHT_THRESHOLD 35.0
#define ROAST_MEDIUM_THRESHOLD 50.0
#define ROAST_MEDIUM_DARK_THRESHOLD 65.0
#define ROAST_DARK_THRESHOLD 80.0

// 构造函数
CoffeeColorDetector::CoffeeColorDetector() {
  currentRGB = RGBColor(0, 0, 0);
  currentLab = LabColor(0, 0, 0);
  currentHSV = HSVColor(0, 0, 0);
  currentResult = ColorAnalysisResult();
}

// 初始化函数
void CoffeeColorDetector::init() {
  // 初始化EEPROM
  EEPROM.begin();
  
  // 从EEPROM加载校准数据（如果有）
  // 这里可以添加加载校准数据的代码
  
  Serial.println("咖啡豆色值检测器初始化完成");
}

// 处理颜色数据
void CoffeeColorDetector::processColorData(const RGBColor& rgb) {
  // 保存原始RGB数据
  currentRGB = rgb;
  
  // 转换到Lab颜色空间
  currentLab = rgbToLab(rgb);
  
  // 转换到HSV颜色空间
  currentHSV = rgbToHsv(rgb);
  
  // 更新当前结果
  currentResult.rgb = rgb;
  currentResult.lab = currentLab;
  currentResult.hsv = currentHSV;
  currentResult.timestamp = millis();
  
  Serial.print("处理颜色数据: R=");
  Serial.print(rgb.red);
  Serial.print(", G=");
  Serial.print(rgb.green);
  Serial.print(", B=");
  Serial.println(rgb.blue);
}

// 分析颜色
void CoffeeColorDetector::analyzeColor() {
  // 这里可以添加更复杂的颜色分析算法
  // 例如：计算色度、饱和度、亮度等参数
  
  Serial.print("分析颜色: L=");
  Serial.print(currentLab.L);
  Serial.print(", a=");
  Serial.print(currentLab.a);
  Serial.print(", b=");
  Serial.println(currentLab.b);
}

// 分析颜色（带参数版本）
void CoffeeColorDetector::analyzeColor(const RGBColor& rgb) {
  // 处理颜色数据
  processColorData(rgb);
  
  // 执行分析
  analyzeColor();
}

// 计算烘焙程度
void CoffeeColorDetector::calculateRoastLevel() {
  // 基于Lab颜色空间的综合值判断烘焙程度
  // 使用L值（亮度）和b值（黄蓝轴）综合评估
  
  float lValue = currentLab.L;
  float bValue = currentLab.b;
  
  Serial.print("计算烘焙程度输入 - L:");
  Serial.print(lValue);
  Serial.print(" b:");
  Serial.println(bValue);
  
  // 计算综合烘焙指数（基于L和b值的加权组合）
  // L值越高表示颜色越浅，b值越高表示越黄（未烘焙）
  float roastIndex = (lValue * 0.6f) + ((bValue + 60.0f) * 0.4f);
  roastIndex = constrain(roastIndex, 10.0f, 90.0f);
  
  // 计算烘焙程度百分比（0-100）
  // 基于综合烘焙指数，越高表示烘焙程度越深
  float percentage = map(roastIndex, 90.0f, 10.0f, 0.0f, 100.0f);
  percentage = constrain(percentage, 0, 100);
  
  // 计算AGTRON值（基于L值的合理范围）
  // AGTRON值范围通常为25-95，数值越大表示颜色越浅
  int agtron = (int)(lValue * 1.0f + 25);
  agtron = constrain(agtron, 25, 95);
  
  // 确定烘焙程度等级
  RoastLevel level;
  if (percentage < ROAST_LIGHT_THRESHOLD) {
    level = ROAST_UNROASTED;
  } else if (percentage < ROAST_MEDIUM_LIGHT_THRESHOLD) {
    level = ROAST_LIGHT;
  } else if (percentage < ROAST_MEDIUM_THRESHOLD) {
    level = ROAST_MEDIUM_LIGHT;
  } else if (percentage < ROAST_MEDIUM_DARK_THRESHOLD) {
    level = ROAST_MEDIUM;
  } else if (percentage < ROAST_DARK_THRESHOLD) {
    level = ROAST_MEDIUM_DARK;
  } else {
    level = ROAST_DARK;
  }
  
  // 更新结果
  currentResult.roastLevel = level;
  currentResult.roastPercentage = percentage;
  currentResult.agtronValue = agtron;
  // 使用AGTRON值来确定烘焙程度描述
  String description = getRoastDescriptionByAgtron(agtron);
  currentResult.roastDescription = description.c_str();
  
  Serial.print("烘焙程度计算结果: ");
  Serial.print(currentResult.roastDescription);
  Serial.print(" (");
  Serial.print(percentage);
  Serial.print("%), AGTRON: ");
  Serial.println(agtron);
}

// RGB转Lab颜色空间 - 优化版本，减少浮点运算
LabColor CoffeeColorDetector::rgbToLab(const RGBColor& rgb) {
  // 首先将RGB归一化到0-1范围，确保最小值避免极端情况
  float r = constrain(rgb.red, 10, 245) / 255.0f;
  float g = constrain(rgb.green, 10, 245) / 255.0f;
  float b = constrain(rgb.blue, 10, 245) / 255.0f;
  
  // 调试输出RGB输入
  Serial.print("RGB转Lab输入 - R:");
  Serial.print(rgb.red);
  Serial.print(" G:");
  Serial.print(rgb.green);
  Serial.print(" B:");
  Serial.print(" 归一化后 - r:");
  Serial.print(r);
  Serial.print(" g:");
  Serial.print(g);
  Serial.print(" b:");
  Serial.println(b);
  
  // 应用Gamma校正 - 使用简化的Gamma校正
  float gamma = 2.2f;
  r = pow(r, 1.0f/gamma);
  g = pow(g, 1.0f/gamma);
  b = pow(b, 1.0f/gamma);
  
  // 转换到XYZ颜色空间 - 使用简化的转换矩阵
  float x = r * 0.4124f + g * 0.3576f + b * 0.1805f;
  float y = r * 0.2126f + g * 0.7152f + b * 0.0722f;
  float z = r * 0.0193f + g * 0.1192f + b * 0.9505f;
  
  // 转换到Lab颜色空间 - 使用简化的转换公式
  // 使用D65标准光源作为参考白点
  float xn = 0.9505f;
  float yn = 1.0000f;
  float zn = 1.0890f;
  
  // 简化的Lab转换
  float fx = x / xn;
  float fy = y / yn;
  float fz = z / zn;
  
  float epsilon = 0.008856f;
  float kappa = 903.3f;
  
  if (fx > epsilon) fx = pow(fx, 1.0f/3.0f);
  else fx = (kappa * fx + 16.0f) / 116.0f;
  
  if (fy > epsilon) fy = pow(fy, 1.0f/3.0f);
  else fy = (kappa * fy + 16.0f) / 116.0f;
  
  if (fz > epsilon) fz = pow(fz, 1.0f/3.0f);
  else fz = (kappa * fz + 16.0f) / 116.0f;
  
  float L = 116.0f * fy - 16.0f;
  float a = 500.0f * (fx - fy);
  float b_val = 200.0f * (fy - fz);
  
  // 限制Lab值的合理范围
  L = constrain(L, 5.0f, 95.0f);
  a = constrain(a, -60.0f, 60.0f);
  b_val = constrain(b_val, -60.0f, 60.0f);
  
  Serial.print("Lab转换结果 - L:");
  Serial.print(L);
  Serial.print(" a:");
  Serial.print(a);
  Serial.print(" b:");
  Serial.println(b_val);
  
  return LabColor(L, a, b_val);
}

// RGB转HSV颜色空间
HSVColor CoffeeColorDetector::rgbToHsv(const RGBColor& rgb) {
  // 将RGB归一化到0-1范围
  float r = rgb.red / 255.0;
  float g = rgb.green / 255.0;
  float b = rgb.blue / 255.0;
  
  float maxVal = max(r, max(g, b));
  float minVal = min(r, min(g, b));
  float delta = maxVal - minVal;
  
  float h, s, v = maxVal;
  
  if (delta == 0) {
    h = 0;
  } else {
    if (maxVal == r) {
      h = fmod(((g - b) / delta), 6);
    } else if (maxVal == g) {
      h = (b - r) / delta + 2;
    } else {
      h = (r - g) / delta + 4;
    }
    h *= 60;
    if (h < 0) h += 360;
  }
  
  s = (maxVal == 0) ? 0 : delta / maxVal;
  
  return HSVColor(h, s, v);
}

// 确定烘焙程度
RoastLevel CoffeeColorDetector::determineRoastLevel(const LabColor& lab) {
  float bValue = lab.b;
  
  if (bValue > 15) return ROAST_UNROASTED;
  else if (bValue > 10) return ROAST_LIGHT;
  else if (bValue > 5) return ROAST_MEDIUM_LIGHT;
  else if (bValue > 0) return ROAST_MEDIUM;
  else if (bValue > -5) return ROAST_MEDIUM_DARK;
  else return ROAST_DARK;
}

// 计算烘焙程度百分比
float CoffeeColorDetector::calculateRoastPercentage(const LabColor& lab) {
  float bValue = lab.b;
  float percentage = map(bValue, -20, 20, 100, 0);
  return constrain(percentage, 0, 100);
}

// 获取烘焙程度描述
String CoffeeColorDetector::getRoastDescription(RoastLevel level) {
  switch (level) {
    case ROAST_UNROASTED: return "未烘焙";
    case ROAST_LIGHT: return "浅烘";
    case ROAST_MEDIUM_LIGHT: return "中浅烘";
    case ROAST_MEDIUM: return "中烘";
    case ROAST_MEDIUM_DARK: return "中深烘";
    case ROAST_DARK: return "深烘";
    default: return "未知";
  }
}

// 根据AGTRON值获取烘焙程度描述
String CoffeeColorDetector::getRoastDescriptionByAgtron(int agtronValue) {
  if (agtronValue >= 80 && agtronValue <= 90) {
    return "极浅烘";
  } else if (agtronValue >= 70 && agtronValue < 80) {
    return "浅烘";
  } else if (agtronValue >= 60 && agtronValue < 70) {
    return "中浅烘";
  } else if (agtronValue >= 50 && agtronValue < 60) {
    return "中烘";
  } else if (agtronValue >= 40 && agtronValue < 50) {
    return "中深烘";
  } else if (agtronValue >= 30 && agtronValue < 40) {
    return "深烘";
  } else if (agtronValue >= 20 && agtronValue < 30) {
    return "极深烘";
  } else {
    return "未知";
  }
}

// 获取分析结果
ColorAnalysisResult CoffeeColorDetector::getAnalysisResult() {
  return currentResult;
}

// 获取RGB值
RGBColor CoffeeColorDetector::getRGB() {
  return currentRGB;
}

// 获取Lab值
LabColor CoffeeColorDetector::getLab() {
  return currentLab;
}

// 获取HSV值
HSVColor CoffeeColorDetector::getHSV() {
  return currentHSV;
}

// 获取烘焙程度
RoastLevel CoffeeColorDetector::getRoastLevel() {
  return currentResult.roastLevel;
}

// 获取烘焙百分比
float CoffeeColorDetector::getRoastPercentage() {
  return currentResult.roastPercentage;
}

// 获取AGTRON值
int CoffeeColorDetector::getAgtronValue() {
  return currentResult.agtronValue;
}

// 保存结果
bool CoffeeColorDetector::saveResult() {
  return saveToMemory(currentResult);
}

// 导出到存储
bool CoffeeColorDetector::saveToMemory(const ColorAnalysisResult& result) {
  // 这里实现将结果保存到EEPROM或SD卡的逻辑
  // 简化实现，仅打印日志
  Serial.println("保存结果到存储器");
  Serial.print("时间戳: ");
  Serial.println(result.timestamp);
  Serial.print("烘焙程度: ");
  Serial.println(result.roastDescription);
  
  // 实际实现中，这里应该将数据写入EEPROM或SD卡
  // 例如：EEPROM.put(address, result);
  
  return true;
}

// 导出结果
bool CoffeeColorDetector::exportResults() {
  // 这里实现将历史记录导出到串口或文件的逻辑
  Serial.println("导出历史记录");
  
  // 实际实现中，这里应该从EEPROM或SD卡读取数据并导出
  // 例如：通过串口发送数据或写入文件
  
  return true;
}

// 获取历史记录数量
int CoffeeColorDetector::getHistoryCount() {
  // 这里实现获取历史记录数量的逻辑
  // 简化实现，返回固定值
  return 0;
}

// 获取历史记录
ColorAnalysisResult CoffeeColorDetector::getHistoryRecord(int index) {
  // 这里实现获取指定索引的历史记录的逻辑
  // 简化实现，返回空结果
  return ColorAnalysisResult();
}

// 清除历史记录
bool CoffeeColorDetector::clearHistory() {
  // 这里实现清除历史记录的逻辑
  Serial.println("清除历史记录");
  
  // 实际实现中，这里应该清除EEPROM或SD卡中的历史记录
  
  return true;
}