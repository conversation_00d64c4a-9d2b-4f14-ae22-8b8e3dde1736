/*
 * OLED显示屏测试程序
 * 测试基本的显示功能
 */

#include <Wire.h>
#include "../src/SimpleOLED.h"

// 创建OLED实例 - 128x64，I2C地址0x3C
SimpleOLED oled(128, 64, 0x3C);

void setup() {
  Serial.begin(9600);
  Serial.println("OLED显示屏测试开始");
  
  // 初始化OLED
  oled.begin();
  Serial.println("OLED初始化完成");
  
  // 测试1：清空屏幕
  oled.clear();
  oled.display();
  delay(1000);
  
  // 测试2：绘制简单像素
  oled.drawPixel(10, 10, 1);
  oled.drawPixel(20, 20, 1);
  oled.drawPixel(30, 30, 1);
  oled.display();
  Serial.println("像素测试完成");
  delay(1000);
  
  // 测试3：绘制线条
  oled.clear();
  oled.drawLine(0, 0, 127, 31, 1);
  oled.display();
  Serial.println("线条测试完成");
  delay(1000);
  
  // 测试4：绘制矩形
  oled.clear();
  oled.drawRect(10, 5, 50, 20, 1);
  oled.fillRect(70, 5, 50, 20, 1);
  oled.display();
  Serial.println("矩形测试完成");
  delay(1000);
  
  // 测试5：绘制数字（SimpleOLED只支持数字）
  oled.clear();
  oled.drawText(10, 10, "12345", 1, 1);
  oled.display();
  Serial.println("数字文本测试完成");
  delay(1000);
  
  // 测试6：显示测试完成
  oled.clear();
  oled.drawText(20, 10, "TEST OK", 1, 1);
  oled.display();
  Serial.println("所有测试完成");
}

void loop() {
  // 测试数字显示
  oled.clear();
  oled.drawText(0, 0, "123", 1, 1);
  oled.display();
  delay(2000);
  
  // 测试英文显示
  oled.clear();
  oled.drawText(0, 0, "ABC", 1, 1);
  oled.display();
  delay(2000);
  
  // 测试混合显示
  oled.clear();
  oled.drawText(0, 0, "A1B2C3", 1, 1);
  oled.display();
  delay(2000);
  
  // 测试完整英文单词
  oled.clear();
  oled.drawText(0, 0, "COFFEE", 1, 1);
  oled.display();
  delay(2000);
  
  // 测试完整英文单词2
  oled.clear();
  oled.drawText(0, 0, "COLOR", 1, 1);
  oled.display();
  delay(2000);
  
  // 测试像素绘制
  oled.clear();
  oled.drawPixel(10, 10, 1);
  oled.drawLine(0, 0, 127, 63, 1);
  oled.drawRect(20, 20, 40, 20, 1);
  oled.display();
  delay(2000);
}