/*
 * ColorSensor.cpp
 * 颜色传感器类实现
 */

#include "../include/ColorSensor.h"
#include <EEPROM.h>

// EEPROM存储地址
#define EEPROM_CALIBRATION_ADDR 0

// TCS3200频率设置
#define TCS3200_FREQ_LOW   0
#define TCS3200_FREQ_MID   1
#define TCS3200_FREQ_HIGH  2
#define TCS3200_FREQ_OFF   3

// TCS3200滤波器设置
#define TCS3200_FILTER_RED    0
#define TCS3200_FILTER_GREEN  1
#define TCS3200_FILTER_BLUE   2
#define TCS3200_FILTER_CLEAR  3

// TCS34725传感器实例已在ColorSensor类中声明为成员变量

// 构造函数
ColorSensor::ColorSensor() {
  config = SensorConfig();
  calibration = CalibrationData();
  initialized = false;
}

ColorSensor::ColorSensor(const SensorConfig& cfg) {
  config = cfg;
  calibration = CalibrationData();
  initialized = false;
}

// 初始化函数
void ColorSensor::init() {
  // 设置引脚模式
  setupPins();
  
  // 根据传感器类型进行初始化
  switch (config.type) {
    case SENSOR_TCS3200:
      initTCS3200();
      break;
    case SENSOR_TCS34725:
      initTCS34725();
      break;
    default:
      Serial.println("不支持的传感器类型");
      return;
  }
  
  initialized = true;
  Serial.println("颜色传感器初始化完成");
}

// 设置配置
void ColorSensor::setConfig(const SensorConfig& cfg) {
  config = cfg;
  if (initialized) {
    init(); // 重新初始化
  }
}

// 获取配置
SensorConfig ColorSensor::getConfig() {
  return config;
}

// 设置引脚
void ColorSensor::setupPins() {
  if (config.type == SENSOR_TCS3200) {
    pinMode(config.s0Pin, OUTPUT);
    pinMode(config.s1Pin, OUTPUT);
    pinMode(config.s2Pin, OUTPUT);
    pinMode(config.s3Pin, OUTPUT);
    pinMode(config.outPin, INPUT);
    
    if (config.ledPin > 0) {
      pinMode(config.ledPin, OUTPUT);
    }
  }
}

// 初始化TCS3200
void ColorSensor::initTCS3200() {
  // 设置输出频率缩放为20%
  setTCS3200Frequency(TCS3200_FREQ_MID);
  
  // 初始关闭LED
  if (config.ledPin > 0) {
    digitalWrite(config.ledPin, LOW);
  }
}

// 初始化TCS34725
void ColorSensor::initTCS34725() {
  // 初始化TCS34725传感器 - 使用软件I2C连接到D4/D5引脚
  if (!tcs34725.begin(config.sdaPin, config.sclPin)) {
    Serial.println("TCS34725初始化失败");
    return;
  }
  
  // 设置积分时间
  setTCS34725IntegrationTime(config.integrationTime);
  
  // 设置增益
  setTCS34725Gain(config.gain);
  
  Serial.println("TCS34725初始化成功");
}

// 读取颜色
RGBColor ColorSensor::readColor() {
  RGBColor rawColor;
  
  switch (config.type) {
    case SENSOR_TCS3200:
      rawColor = readTCS3200();
      break;
    case SENSOR_TCS34725:
      rawColor = readTCS34725();
      break;
    default:
      Serial.println("不支持的传感器类型");
      return RGBColor(0, 0, 0);
  }
  
  // 应用校准
  return applyCalibration(rawColor);
}

// 读取TCS3200颜色
RGBColor ColorSensor::readTCS3200() {
  uint16_t red, green, blue, clear;
  
  // 启用LED
  enableLED(true);
  delay(100); // 等待LED稳定
  
  // 读取红色
  setTCS3200Filter(true, false, false);  // 红色滤波器
  delay(10); // 等待滤波器稳定
  red = pulseIn(config.outPin, LOW);
  
  // 读取绿色
  setTCS3200Filter(false, true, false);  // 绿色滤波器
  delay(10);
  green = pulseIn(config.outPin, LOW);
  
  // 读取蓝色
  setTCS3200Filter(false, false, true);  // 蓝色滤波器
  delay(10);
  blue = pulseIn(config.outPin, LOW);
  
  // 读取透明度
  setTCS3200Filter(false, false, false);  // 清除滤波器
  delay(10);
  clear = pulseIn(config.outPin, LOW);
  
  // 关闭LED
  enableLED(false);
  
  // 将脉冲宽度转换为0-255范围
  // 注意：脉冲宽度与光强成反比，所以需要反转
  red = map(red, 0, clear, 255, 0);
  green = map(green, 0, clear, 255, 0);
  blue = map(blue, 0, clear, 255, 0);
  
  // 确保值在有效范围内
  red = constrain(red, 0, 255);
  green = constrain(green, 0, 255);
  blue = constrain(blue, 0, 255);
  
  return RGBColor(red, green, blue);
}

// 读取TCS34725颜色
RGBColor ColorSensor::readTCS34725() {
  uint16_t red, green, blue, clear;
  
  // 读取原始数据
  tcs34725.getRawData(&red, &green, &blue, &clear);
  
  // 调试输出原始值
  Serial.print("TCS34725原始数据 - R:");
  Serial.print(red);
  Serial.print(" G:");
  Serial.print(green);
  Serial.print(" B:");
  Serial.print(blue);
  Serial.print(" C:");
  Serial.println(clear);
  
  // 检查clear值，避免除零和异常值
  if (clear == 0 || clear < 100) {
    Serial.println("警告：Clear值异常，使用默认校准");
    // 返回一个合理的默认值，避免纯白
    return RGBColor(128, 128, 128);
  }
  
  // 使用更合理的映射方法
  // 基于比例计算，但设置最小阈值避免极端值
  float red_ratio = (float)red / clear;
  float green_ratio = (float)green / clear;
  float blue_ratio = (float)blue / clear;
  
  // 转换为0-255范围，设置最小值避免纯黑
  red = (uint16_t)(red_ratio * 255.0 * 0.8 + 20);
  green = (uint16_t)(green_ratio * 255.0 * 0.8 + 20);
  blue = (uint16_t)(blue_ratio * 255.0 * 0.8 + 20);
  
  // 确保值在有效范围内
  red = constrain(red, 10, 245);
  green = constrain(green, 10, 245);
  blue = constrain(blue, 10, 245);
  
  Serial.print("转换后RGB - R:");
  Serial.print(red);
  Serial.print(" G:");
  Serial.print(green);
  Serial.print(" B:");
  Serial.println(blue);
  
  return RGBColor(red, green, blue);
}

// 读取红色值
uint16_t ColorSensor::readRed() {
  return readColor().red;
}

// 读取绿色值
uint16_t ColorSensor::readGreen() {
  return readColor().green;
}

// 读取蓝色值
uint16_t ColorSensor::readBlue() {
  return readColor().blue;
}

// 读取透明度值
uint16_t ColorSensor::readClear() {
  if (config.type == SENSOR_TCS34725) {
    uint16_t red, green, blue, clear;
    tcs34725.getRawData(&red, &green, &blue, &clear);
    return clear;
  }
  
  // 对于TCS3200，返回固定值
  return 255;
}

// 设置TCS3200频率
void ColorSensor::setTCS3200Frequency(bool freq) {
  digitalWrite(config.s0Pin, freq & 0x01);
  digitalWrite(config.s1Pin, (freq >> 1) & 0x01);
}

// 设置TCS3200滤波器
void ColorSensor::setTCS3200Filter(bool red, bool green, bool blue) {
  digitalWrite(config.s2Pin, red);
  digitalWrite(config.s3Pin, green);
}

// 设置TCS34725积分时间
void ColorSensor::setTCS34725IntegrationTime(uint8_t time) {
  tcs34725.setIntegrationTime(time);
}

// 设置TCS34725增益
void ColorSensor::setTCS34725Gain(uint8_t gain) {
  tcs34725.setGain(gain);
}

// 启用/禁用LED
void ColorSensor::enableLED(bool enable) {
  if (config.ledPin > 0) {
    digitalWrite(config.ledPin, enable ? HIGH : LOW);
  }
}

// 应用校准
RGBColor ColorSensor::applyCalibration(const RGBColor& raw) {
  if (!calibration.isCalibrated) {
    return raw;
  }
  
  // 计算校准后的值
  uint16_t red = map(raw.red, calibration.blackRed, calibration.whiteRed, 0, 255);
  uint16_t green = map(raw.green, calibration.blackGreen, calibration.whiteGreen, 0, 255);
  uint16_t blue = map(raw.blue, calibration.blackBlue, calibration.whiteBlue, 0, 255);
  
  // 确保值在有效范围内
  red = constrain(red, 0, 255);
  green = constrain(green, 0, 255);
  blue = constrain(blue, 0, 255);
  
  return RGBColor(red, green, blue);
}

// 校准白色
void ColorSensor::calibrateWhite() {
  Serial.println("开始白色校准...");
  
  // 启用LED
  enableLED(true);
  delay(500); // 等待稳定
  
  // 读取白色参考值
  RGBColor white = readColor();
  calibration.whiteRed = white.red;
  calibration.whiteGreen = white.green;
  calibration.whiteBlue = white.blue;
  
  // 关闭LED
  enableLED(false);
  
  calibration.isCalibrated = true;
  
  // 保存校准数据到EEPROM
  saveCalibrationToEEPROM();
  
  Serial.print("白色校准完成: R=");
  Serial.print(calibration.whiteRed);
  Serial.print(", G=");
  Serial.print(calibration.whiteGreen);
  Serial.print(", B=");
  Serial.println(calibration.whiteBlue);
}

// 校准黑色
void ColorSensor::calibrateBlack() {
  Serial.println("开始黑色校准...");
  
  // 确保LED关闭
  enableLED(false);
  delay(500); // 等待稳定
  
  // 读取黑色参考值
  RGBColor black = readColor();
  calibration.blackRed = black.red;
  calibration.blackGreen = black.green;
  calibration.blackBlue = black.blue;
  
  calibration.isCalibrated = true;
  
  // 保存校准数据到EEPROM
  saveCalibrationToEEPROM();
  
  Serial.print("黑色校准完成: R=");
  Serial.print(calibration.blackRed);
  Serial.print(", G=");
  Serial.print(calibration.blackGreen);
  Serial.print(", B=");
  Serial.println(calibration.blackBlue);
}

// 重置校准
void ColorSensor::resetCalibration() {
  calibration = CalibrationData();
  
  // 清除EEPROM中的校准数据
  clearCalibrationFromEEPROM();
  
  Serial.println("校准数据已重置");
}

// 获取校准数据
CalibrationData ColorSensor::getCalibrationData() {
  return calibration;
}

// 设置校准数据
void ColorSensor::setCalibrationData(const CalibrationData& data) {
  calibration = data;
}

// 检查是否已初始化
bool ColorSensor::isInitialized() {
  return initialized;
}

// 检查传感器是否可用
bool ColorSensor::isAvailable() {
  if (!initialized) {
    return false;
  }
  
  // TCS34725传感器可用性检查
  if (config.type == SENSOR_TCS34725) {
    // 尝试读取设备ID
    uint8_t id = tcs34725.read8(TCS34725_ID);
    Serial.print("TCS34725设备ID: 0x");
    Serial.println(id, HEX);
    
    // 检查ID是否为有效值
    if (id != 0x44 && id != 0x4D && id != 0x10) {
      Serial.println("TCS34725设备ID无效！");
      return false;
    }
    
    // 尝试读取状态寄存器
    uint8_t status = tcs34725.read8(TCS34725_STATUS);
    Serial.print("TCS34725状态: 0x");
    Serial.println(status, HEX);
    
    return true;
  }
  
  return true;
}

// 设置LED状态
void ColorSensor::setLED(bool enable) {
  enableLED(enable);
}

// 检查LED是否启用
bool ColorSensor::isLEDEnabled() {
  if (config.ledPin > 0) {
    return digitalRead(config.ledPin) == HIGH;
  }
  return false;
}

// 保存校准数据到EEPROM
void ColorSensor::saveCalibrationToEEPROM() {
  int addr = EEPROM_CALIBRATION_ADDR;
  
  // 写入校准数据
  EEPROM.put(addr, calibration);
  
  Serial.println("校准数据已保存到EEPROM");
}

// 从EEPROM加载校准数据
void ColorSensor::loadCalibrationFromEEPROM() {
  int addr = EEPROM_CALIBRATION_ADDR;
  
  // 读取校准数据
  CalibrationData storedData;
  EEPROM.get(addr, storedData);
  
  // 检查数据是否有效
  if (storedData.isCalibrated) {
    calibration = storedData;
    Serial.println("从EEPROM加载校准数据成功");
  } else {
    Serial.println("EEPROM中没有有效的校准数据");
  }
}

// 清除EEPROM中的校准数据
void ColorSensor::clearCalibrationFromEEPROM() {
  int addr = EEPROM_CALIBRATION_ADDR;
  
  // 创建一个空的校准数据结构
  CalibrationData emptyData;
  
  // 写入空数据
  EEPROM.put(addr, emptyData);
  
  Serial.println("EEPROM中的校准数据已清除");
}