/*
 * UserInterface.h
 * 用户界面类定义
 * 
 * 主要功能：
 * 1. 初始化用户输入设备
 * 2. 处理用户输入
 * 3. 提供菜单导航
 * 4. 管理用户交互
 */

#ifndef USER_INTERFACE_H
#define USER_INTERFACE_H

#include <Arduino.h>

// 按键类型枚举
enum ButtonType {
  BUTTON_UP,         // 上键
  BUTTON_DOWN,       // 下键
  BUTTON_LEFT,       // 左键
  BUTTON_RIGHT,      // 右键
  BUTTON_SELECT,     // 选择键
  BUTTON_BACK,       // 返回键
  BUTTON_MEASURE,    // 测量键
  BUTTON_SAVE,       // 保存键
  BUTTON_MENU,       // 菜单键
  BUTTON_CUSTOM      // 自定义键
};

// 输入设备类型枚举
enum InputDeviceType {
  INPUT_BUTTONS,     // 物理按键
  INPUT_ENCODER,     // 旋转编码器
  INPUT_TOUCH,       // 触摸屏
  INPUT_SERIAL,      // 串口输入
  INPUT_CUSTOM       // 自定义输入
};

// 按键配置结构体
struct ButtonConfig {
  ButtonType type;
  uint8_t pin;           // 引脚号
  bool pullup;           // 是否启用上拉电阻
  bool debounce;         // 是否启用消抖
  uint16_t debounceTime; // 消抖时间(毫秒)
  bool invert;           // 是否反转逻辑
  
  ButtonConfig() : 
    type(BUTTON_CUSTOM), pin(0), pullup(true), 
    debounce(true), debounceTime(50), invert(false) {}
};

// 输入设备配置结构体
struct InputDeviceConfig {
  InputDeviceType type;
  ButtonConfig buttons[10];  // 按键配置
  uint8_t buttonCount;       // 按键数量
  
  // 编码器配置
  uint8_t encoderPinA;
  uint8_t encoderPinB;
  uint8_t encoderButtonPin;
  
  // 触摸屏配置
  uint8_t touchCsPin;
  uint8_t touchIrqPin;
  
  InputDeviceConfig() : 
    type(INPUT_BUTTONS), buttonCount(0),
    encoderPinA(0), encoderPinB(0), encoderButtonPin(0),
    touchCsPin(0), touchIrqPin(0) {}
};

// 按键状态结构体 - 优化版本，减少内存使用
struct ButtonState {
  bool pressed;         // 当前是否按下
  bool released;        // 当前是否释放
  bool longPress;       // 是否长按
  bool doubleClick;     // 是否双击
  uint32_t pressTime;   // 按下时间
  uint32_t releaseTime; // 释放时间
  
  ButtonState() : 
    pressed(false), released(false), longPress(false), doubleClick(false), pressTime(0), releaseTime(0) {}
};

// 菜单项结构体 - 优化版本，减少内存使用
struct MenuItem {
  const char* text;     // 菜单文本
  int id;               // 菜单ID
  MenuItem* parent;     // 父菜单
  MenuItem* children;   // 子菜单
  uint8_t childCount;   // 子菜单数量 - 减少最大子菜单数量
  bool selectable;      // 是否可选
  void (*callback)();   // 菜单项回调函数
  
  MenuItem() : 
    text(""), id(-1), parent(nullptr), children(nullptr), 
    childCount(0), selectable(true), callback(nullptr) {}
};

// 用户界面类
class UserInterface {
private:
  InputDeviceConfig config;
  bool initialized;
  
  // 按键状态 - 减少数组大小以节省内存
  ButtonState buttonStates[5];
  uint8_t buttonCount;
  
  // 菜单系统
  MenuItem* currentMenu;
  MenuItem* rootMenu;
  int currentSelection;
  int menuLevel;
  
  // 输入处理函数
  void processButtons();
  void processEncoder();
  void processTouch();
  void processSerial();
  
  // 按键处理函数
  void updateButtonState(int index);
  bool isButtonPressedInternal(int index);
  bool isButtonReleasedInternal(int index);
  bool isButtonLongPressedInternal(int index);
  bool isButtonDoubleClickedInternal(int index);
  
  // 菜单处理函数
  void initMenuSystem();
  void navigateMenu(int direction);
  void selectMenuItem();
  void backToParentMenu();
  void executeMenuCallback();
  
  // 事件回调函数
  void (*buttonPressCallback)(ButtonType button);
  void (*menuSelectCallback)(int menuId);
  void (*menuBackCallback)();
  
public:
  // 构造函数
  UserInterface();
  UserInterface(const InputDeviceConfig& cfg);
  
  // 初始化和配置
  void init();
  void setConfig(const InputDeviceConfig& cfg);
  InputDeviceConfig getConfig();
  
  // 输入处理
  void processInput();
  
  // 按键状态查询 - 优化版本，减少内存使用
  bool isButtonPressed(ButtonType button);
  bool isButtonReleased(ButtonType button);
  bool isButtonLongPressed(ButtonType button);
  bool isButtonDoubleClicked(ButtonType button);
  
  // 特殊按键状态查询 - 优化版本，减少内存使用
  bool isUpButtonPressed();
  bool isDownButtonPressed();
  bool isLeftButtonPressed();
  bool isRightButtonPressed();
  bool isSelectButtonPressed();
  bool isBackButtonPressed();
  bool isMeasureButtonPressed();
  bool isSaveButtonPressed();
  bool isMenuButtonPressed();
  
  // 菜单系统
  void setRootMenu(MenuItem* menu);
  void setCurrentMenu(MenuItem* menu);
  MenuItem* getCurrentMenu();
  int getMenuSelection();
  void setMenuSelection(int selection);
  void navigateUp();
  void navigateDown();
  void selectMenu();
  void backMenu();
  
  // 回调设置
  void setButtonPressCallback(void (*callback)(ButtonType button));
  void setMenuSelectCallback(void (*callback)(int menuId));
  void setMenuBackCallback(void (*callback)());
  
  // 状态检查
  bool isInitialized();
  bool isInputAvailable();
  
  // 显示功能
  void displayMeasurementResult(int agtronValue);
  void displayStatus(const char* status);
  
  // 调试功能
  void printButtonStates();
  void printMenuStructure();
};

#endif // USER_INTERFACE_H